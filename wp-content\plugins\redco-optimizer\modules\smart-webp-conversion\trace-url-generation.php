<?php
/**
 * Trace URL Generation - Step by step analysis of how WordPress generates attachment URLs
 */

// Load WordPress
require_once('../../../../../../wp-load.php');

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🔍 Trace URL Generation Process</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .warning { color: orange; } .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; } .step { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }</style>";

$attachment_id = 15;

echo "<h2>Tracing URL Generation for Attachment ID: {$attachment_id}</h2>";

// Step 1: Check the raw metadata
echo "<div class='step'>";
echo "<h3>Step 1: Raw Database Values</h3>";

global $wpdb;
$attached_file_raw = $wpdb->get_var($wpdb->prepare("
    SELECT meta_value FROM {$wpdb->postmeta} 
    WHERE post_id = %d AND meta_key = '_wp_attached_file'
", $attachment_id));

echo "<p><strong>_wp_attached_file (raw from DB):</strong> {$attached_file_raw}</p>";

$metadata_raw = $wpdb->get_var($wpdb->prepare("
    SELECT meta_value FROM {$wpdb->postmeta} 
    WHERE post_id = %d AND meta_key = '_wp_attachment_metadata'
", $attachment_id));

echo "<p><strong>_wp_attachment_metadata (raw from DB):</strong></p>";
echo "<div class='code'>" . $metadata_raw . "</div>";
echo "</div>";

// Step 2: WordPress functions step by step
echo "<div class='step'>";
echo "<h3>Step 2: WordPress Function Chain</h3>";

// Test get_post_meta directly
$attached_file_meta = get_post_meta($attachment_id, '_wp_attached_file', true);
echo "<p><strong>get_post_meta('_wp_attached_file'):</strong> {$attached_file_meta}</p>";

// Test get_attached_file
$attached_file_func = get_attached_file($attachment_id);
echo "<p><strong>get_attached_file():</strong> {$attached_file_func}</p>";

// Check if they're different
if ($attached_file_meta !== $attached_file_func) {
    echo "<p class='warning'>⚠️ get_post_meta and get_attached_file return different values!</p>";
}
echo "</div>";

// Step 3: Upload directory analysis
echo "<div class='step'>";
echo "<h3>Step 3: Upload Directory Analysis</h3>";

$upload_dir = wp_upload_dir();
echo "<div class='code'>";
foreach ($upload_dir as $key => $value) {
    echo "{$key}: {$value}\n";
}
echo "</div>";

// Check if basedir and baseurl are properly configured
$basedir_exists = is_dir($upload_dir['basedir']);
$baseurl_accessible = (strpos($upload_dir['baseurl'], 'http') === 0);

echo "<p>Upload basedir exists: " . ($basedir_exists ? "✅ YES" : "❌ NO") . "</p>";
echo "<p>Upload baseurl is web URL: " . ($baseurl_accessible ? "✅ YES" : "❌ NO") . "</p>";
echo "</div>";

// Step 4: Manual URL construction
echo "<div class='step'>";
echo "<h3>Step 4: Manual URL Construction</h3>";

if ($attached_file_meta) {
    // Check if attached_file is already a full path
    $is_full_path = (strpos($attached_file_meta, $upload_dir['basedir']) === 0);
    echo "<p>Attached file is full path: " . ($is_full_path ? "❌ YES" : "✅ NO") . "</p>";
    
    if ($is_full_path) {
        // It's a full path, convert to URL
        $manual_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $attached_file_meta);
        echo "<p><strong>Manual URL (from full path):</strong> {$manual_url}</p>";
    } else {
        // It's a relative path, construct URL properly
        $manual_url = $upload_dir['baseurl'] . '/' . ltrim($attached_file_meta, '/');
        echo "<p><strong>Manual URL (from relative path):</strong> {$manual_url}</p>";
    }
    
    // Normalize the URL
    $normalized_url = wp_normalize_path($manual_url);
    $normalized_url = str_replace('\\', '/', $normalized_url);
    echo "<p><strong>Normalized URL:</strong> {$normalized_url}</p>";
}
echo "</div>";

// Step 5: WordPress core wp_get_attachment_url analysis
echo "<div class='step'>";
echo "<h3>Step 5: WordPress Core wp_get_attachment_url Analysis</h3>";

// Hook into wp_get_attachment_url to see what's happening
function debug_wp_get_attachment_url($url, $post_id) {
    if ($post_id == 15) { // Our test attachment
        echo "<p class='code'>wp_get_attachment_url filter called:<br>";
        echo "Input URL: {$url}<br>";
        echo "Post ID: {$post_id}</p>";
    }
    return $url;
}

add_filter('wp_get_attachment_url', 'debug_wp_get_attachment_url', 1, 2);

$wp_url = wp_get_attachment_url($attachment_id);
echo "<p><strong>wp_get_attachment_url result:</strong> {$wp_url}</p>";

remove_filter('wp_get_attachment_url', 'debug_wp_get_attachment_url', 1);
echo "</div>";

// Step 6: Check WordPress constants and configuration
echo "<div class='step'>";
echo "<h3>Step 6: WordPress Configuration</h3>";

echo "<div class='code'>";
echo "WP_CONTENT_URL: " . (defined('WP_CONTENT_URL') ? WP_CONTENT_URL : 'Not defined') . "\n";
echo "WP_CONTENT_DIR: " . (defined('WP_CONTENT_DIR') ? WP_CONTENT_DIR : 'Not defined') . "\n";
echo "UPLOADS: " . (defined('UPLOADS') ? UPLOADS : 'Not defined') . "\n";
echo "ABSPATH: " . ABSPATH . "\n";
echo "site_url(): " . site_url() . "\n";
echo "home_url(): " . home_url() . "\n";
echo "</div>";
echo "</div>";

// Step 7: Check for plugin conflicts
echo "<div class='step'>";
echo "<h3>Step 7: Plugin Conflict Check</h3>";

// List all active plugins
$active_plugins = get_option('active_plugins');
echo "<p><strong>Active Plugins:</strong></p>";
echo "<div class='code'>";
foreach ($active_plugins as $plugin) {
    echo $plugin . "\n";
}
echo "</div>";

// Check if any plugins are filtering attachment URLs
global $wp_filter;
if (isset($wp_filter['wp_get_attachment_url'])) {
    echo "<p><strong>Plugins filtering wp_get_attachment_url:</strong></p>";
    echo "<div class='code'>";
    foreach ($wp_filter['wp_get_attachment_url']->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (is_array($callback['function'])) {
                $class = is_object($callback['function'][0]) ? get_class($callback['function'][0]) : $callback['function'][0];
                echo "Priority {$priority}: {$class}::{$callback['function'][1]}\n";
            } else {
                echo "Priority {$priority}: {$callback['function']}\n";
            }
        }
    }
    echo "</div>";
}
echo "</div>";

// Step 8: Diagnosis and recommendations
echo "<div class='step'>";
echo "<h3>Step 8: Diagnosis</h3>";

$url_is_local_path = (strpos($wp_url, 'D:') === 0 || strpos($wp_url, 'C:') === 0);
$attached_file_is_full_path = (strpos($attached_file_meta, $upload_dir['basedir']) === 0);

if ($url_is_local_path) {
    echo "<p class='error'>🚨 CONFIRMED: wp_get_attachment_url is returning a local file path</p>";
    
    if ($attached_file_is_full_path) {
        echo "<p class='error'>🎯 ROOT CAUSE: _wp_attached_file contains full file system path instead of relative path</p>";
        echo "<p><strong>Solution:</strong> Fix the _wp_attached_file metadata to use relative paths</p>";
    } else {
        echo "<p class='warning'>🤔 MYSTERY: _wp_attached_file looks correct but wp_get_attachment_url still returns local path</p>";
        echo "<p><strong>Possible causes:</strong> Plugin conflict, WordPress core issue, or upload directory misconfiguration</p>";
    }
} else {
    echo "<p class='success'>✅ wp_get_attachment_url is returning a proper URL</p>";
}

echo "<h4>🔧 Recommended Actions:</h4>";
echo "<ol>";
if ($attached_file_is_full_path) {
    echo "<li><strong>Fix metadata:</strong> Update _wp_attached_file to use relative paths</li>";
}
echo "<li><strong>Check upload directory configuration</strong> in WordPress settings</li>";
echo "<li><strong>Temporarily deactivate other plugins</strong> to check for conflicts</li>";
echo "<li><strong>Check WordPress wp-config.php</strong> for custom upload directory settings</li>";
echo "</ol>";
echo "</div>";

echo "<p><a href='deep-debug.php'>🔍 Run Deep Debug Tool</a> | <a href='test-url-fix.php'>🧪 Test URL Fix</a></p>";
