<?php
/**
 * WebP File Deletion Test Suite
 * 
 * This file provides comprehensive testing for the WebP file deletion functionality
 * to ensure all files are properly removed when images are deleted through WordPress media library.
 * 
 * Usage: Include this file and call the test functions to validate the deletion process.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_WebP_Deletion_Test {
    
    private $test_results = array();
    private $webp_converter;
    
    public function __construct() {
        $this->webp_converter = new Redco_Smart_WebP_Conversion();
    }
    
    /**
     * Run comprehensive file deletion tests
     */
    public function run_all_tests() {
        echo "<h2>🧪 WebP File Deletion Test Suite</h2>\n";
        
        $this->test_database_integrity();
        $this->test_file_path_storage();
        $this->test_deletion_hooks();
        $this->test_file_cleanup();
        $this->test_orphaned_cleanup();
        
        $this->display_results();
    }
    
    /**
     * Test 1: Database Integrity Analysis
     */
    private function test_database_integrity() {
        echo "<h3>📊 Test 1: Database Integrity Analysis</h3>\n";
        
        global $wpdb;
        
        // Check wp_posts table for attachments
        $attachment_count = $wpdb->get_var("
            SELECT COUNT(*) 
            FROM {$wpdb->posts} 
            WHERE post_type = 'attachment' 
            AND post_mime_type LIKE 'image/%'
        ");
        
        echo "<p>✅ Found {$attachment_count} image attachments in wp_posts table</p>\n";
        
        // Check wp_postmeta for WebP conversion data
        $webp_meta_count = $wpdb->get_var("
            SELECT COUNT(*) 
            FROM {$wpdb->postmeta} 
            WHERE meta_key = '_webp_conversion_data'
        ");
        
        echo "<p>✅ Found {$webp_meta_count} WebP conversion metadata entries</p>\n";
        
        // Check for file path accuracy
        $sample_attachments = $wpdb->get_results("
            SELECT p.ID, pm.meta_value as file_path
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_wp_attached_file'
            LIMIT 5
        ");
        
        $valid_paths = 0;
        foreach ($sample_attachments as $attachment) {
            $upload_dir = wp_upload_dir();
            $full_path = $upload_dir['basedir'] . '/' . $attachment->file_path;
            if (file_exists($full_path)) {
                $valid_paths++;
            }
        }
        
        echo "<p>✅ {$valid_paths} out of " . count($sample_attachments) . " sample file paths are valid</p>\n";
        
        $this->test_results['database_integrity'] = array(
            'attachment_count' => $attachment_count,
            'webp_meta_count' => $webp_meta_count,
            'valid_paths_ratio' => count($sample_attachments) > 0 ? $valid_paths / count($sample_attachments) : 0
        );
    }
    
    /**
     * Test 2: File Path Storage Verification
     */
    private function test_file_path_storage() {
        echo "<h3>📁 Test 2: File Path Storage Verification</h3>\n";
        
        global $wpdb;
        
        // Get sample WebP conversion data
        $webp_conversions = $wpdb->get_results("
            SELECT post_id, meta_value 
            FROM {$wpdb->postmeta} 
            WHERE meta_key = '_webp_conversion_data' 
            LIMIT 3
        ");
        
        $valid_webp_paths = 0;
        $total_webp_files = 0;
        
        foreach ($webp_conversions as $conversion) {
            $data = maybe_unserialize($conversion->meta_value);
            
            if (is_array($data)) {
                // Check main WebP file
                if (isset($data['webp_path']) && $data['webp_path']) {
                    $total_webp_files++;
                    if (file_exists($data['webp_path'])) {
                        $valid_webp_paths++;
                        echo "<p>✅ Main WebP file exists: " . basename($data['webp_path']) . "</p>\n";
                    } else {
                        echo "<p>❌ Main WebP file missing: " . basename($data['webp_path']) . "</p>\n";
                    }
                }
                
                // Check WebP thumbnails
                if (isset($data['webp_sizes']) && is_array($data['webp_sizes'])) {
                    foreach ($data['webp_sizes'] as $size_name => $webp_path) {
                        $total_webp_files++;
                        if (file_exists($webp_path)) {
                            $valid_webp_paths++;
                            echo "<p>✅ WebP thumbnail ({$size_name}) exists: " . basename($webp_path) . "</p>\n";
                        } else {
                            echo "<p>❌ WebP thumbnail ({$size_name}) missing: " . basename($webp_path) . "</p>\n";
                        }
                    }
                }
            }
        }
        
        $this->test_results['file_path_storage'] = array(
            'total_webp_files' => $total_webp_files,
            'valid_webp_paths' => $valid_webp_paths,
            'validity_ratio' => $total_webp_files > 0 ? $valid_webp_paths / $total_webp_files : 0
        );
    }
    
    /**
     * Test 3: File Deletion Hook Investigation
     */
    private function test_deletion_hooks() {
        echo "<h3>🔗 Test 3: File Deletion Hook Investigation</h3>\n";
        
        // Check if delete_attachment hook is registered
        $hook_registered = has_action('delete_attachment', array($this->webp_converter, 'delete_webp_versions'));
        
        if ($hook_registered !== false) {
            echo "<p>✅ delete_attachment hook is properly registered</p>\n";
        } else {
            echo "<p>❌ delete_attachment hook is NOT registered</p>\n";
        }
        
        // Check if cleanup hook is registered
        $cleanup_hook = has_action('wp_scheduled_delete', array($this->webp_converter, 'cleanup_orphaned_webp_files'));
        
        if ($cleanup_hook !== false) {
            echo "<p>✅ wp_scheduled_delete cleanup hook is properly registered</p>\n";
        } else {
            echo "<p>❌ wp_scheduled_delete cleanup hook is NOT registered</p>\n";
        }
        
        $this->test_results['deletion_hooks'] = array(
            'delete_attachment_registered' => $hook_registered !== false,
            'cleanup_hook_registered' => $cleanup_hook !== false
        );
    }
    
    /**
     * Test 4: File System Cleanup Verification
     */
    private function test_file_cleanup() {
        echo "<h3>🗑️ Test 4: File System Cleanup Verification</h3>\n";
        
        // Test the deletion method without actually deleting files
        echo "<p>Testing deletion method functionality...</p>\n";
        
        global $wpdb;
        
        // Get a sample converted attachment
        $sample_attachment = $wpdb->get_row("
            SELECT p.ID 
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
            AND pm.meta_value LIKE '%converted%'
            LIMIT 1
        ");
        
        if ($sample_attachment) {
            $conversion_data = get_post_meta($sample_attachment->ID, '_webp_conversion_data', true);
            
            if ($conversion_data && is_array($conversion_data)) {
                $files_to_check = array();
                
                // Add main WebP file
                if (isset($conversion_data['webp_path'])) {
                    $files_to_check[] = $conversion_data['webp_path'];
                }
                
                // Add thumbnail files
                if (isset($conversion_data['webp_sizes']) && is_array($conversion_data['webp_sizes'])) {
                    $files_to_check = array_merge($files_to_check, array_values($conversion_data['webp_sizes']));
                }
                
                $existing_files = 0;
                foreach ($files_to_check as $file_path) {
                    if (file_exists($file_path)) {
                        $existing_files++;
                        echo "<p>✅ File exists and would be deleted: " . basename($file_path) . "</p>\n";
                    } else {
                        echo "<p>⚠️ File already missing: " . basename($file_path) . "</p>\n";
                    }
                }
                
                echo "<p>📊 Total files that would be processed: " . count($files_to_check) . "</p>\n";
                echo "<p>📊 Files currently existing: {$existing_files}</p>\n";
                
                $this->test_results['file_cleanup'] = array(
                    'total_files_to_process' => count($files_to_check),
                    'existing_files' => $existing_files,
                    'sample_attachment_id' => $sample_attachment->ID
                );
            } else {
                echo "<p>❌ No valid conversion data found for sample attachment</p>\n";
            }
        } else {
            echo "<p>⚠️ No converted attachments found for testing</p>\n";
        }
    }
    
    /**
     * Test 5: Orphaned File Cleanup
     */
    private function test_orphaned_cleanup() {
        echo "<h3>🧹 Test 5: Orphaned File Cleanup</h3>\n";
        
        // Test orphaned file detection without actually cleaning
        $upload_dir = wp_upload_dir();
        $upload_path = $upload_dir['basedir'];
        
        echo "<p>Scanning for WebP files in: {$upload_path}</p>\n";
        
        // Use the converter's method to find WebP files
        $reflection = new ReflectionClass($this->webp_converter);
        $method = $reflection->getMethod('find_webp_files_recursive');
        $method->setAccessible(true);
        
        $webp_files = $method->invoke($this->webp_converter, $upload_path);
        
        echo "<p>✅ Found " . count($webp_files) . " WebP files total</p>\n";
        
        $orphaned_count = 0;
        $referenced_count = 0;
        
        foreach (array_slice($webp_files, 0, 10) as $webp_file) { // Test first 10 files
            $original_method = $reflection->getMethod('get_original_from_webp_path');
            $original_method->setAccessible(true);
            $original_file = $original_method->invoke($this->webp_converter, $webp_file);
            
            $referenced_method = $reflection->getMethod('is_webp_file_referenced');
            $referenced_method->setAccessible(true);
            $is_referenced = $referenced_method->invoke($this->webp_converter, $webp_file);
            
            if (!$original_file || !file_exists($original_file)) {
                if (!$is_referenced) {
                    $orphaned_count++;
                    echo "<p>⚠️ Orphaned file detected: " . basename($webp_file) . "</p>\n";
                } else {
                    $referenced_count++;
                }
            }
        }
        
        $this->test_results['orphaned_cleanup'] = array(
            'total_webp_files' => count($webp_files),
            'orphaned_detected' => $orphaned_count,
            'referenced_files' => $referenced_count
        );
    }
    
    /**
     * Display comprehensive test results
     */
    private function display_results() {
        echo "<h2>📋 Test Results Summary</h2>\n";
        echo "<pre>" . print_r($this->test_results, true) . "</pre>\n";
        
        // Overall assessment
        $issues = array();
        
        if ($this->test_results['deletion_hooks']['delete_attachment_registered'] === false) {
            $issues[] = "delete_attachment hook not registered";
        }
        
        if (isset($this->test_results['file_path_storage']['validity_ratio']) && 
            $this->test_results['file_path_storage']['validity_ratio'] < 0.8) {
            $issues[] = "Low file path validity ratio";
        }
        
        if (empty($issues)) {
            echo "<h3 style='color: green;'>✅ All tests passed! File deletion should work correctly.</h3>\n";
        } else {
            echo "<h3 style='color: red;'>❌ Issues detected:</h3>\n";
            foreach ($issues as $issue) {
                echo "<p style='color: red;'>• {$issue}</p>\n";
            }
        }
    }
}

// Usage example (uncomment to run tests):
// if (current_user_can('manage_options')) {
//     $test_suite = new Redco_WebP_Deletion_Test();
//     $test_suite->run_all_tests();
// }
