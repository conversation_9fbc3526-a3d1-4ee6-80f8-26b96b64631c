<?php
/**
 * Enable Debug Mode and Test WebP Path Fixing
 */

// Load WordPress
require_once('../../../../../../wp-load.php');

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🔧 Enable Debug & Test WebP Path Fixing</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .warning { color: orange; } .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }</style>";

// Check if WP_DEBUG is enabled
$debug_enabled = defined('WP_DEBUG') && WP_DEBUG;
echo "<p>WP_DEBUG currently: " . ($debug_enabled ? "<span class='success'>✅ ENABLED</span>" : "<span class='error'>❌ DISABLED</span>") . "</p>";

if (!$debug_enabled) {
    echo "<h2>🔧 Enabling Debug Mode Temporarily</h2>";
    echo "<p>To see detailed debug logs, we need to enable WP_DEBUG. This will be done temporarily for testing.</p>";
    
    // Try to enable debug mode temporarily (this won't persist)
    if (!defined('WP_DEBUG')) {
        define('WP_DEBUG', true);
        echo "<p class='success'>✅ WP_DEBUG enabled for this request</p>";
    }
    
    echo "<p class='warning'>⚠️ For persistent debugging, add this to your wp-config.php:</p>";
    echo "<div class='code'>define('WP_DEBUG', true);<br>define('WP_DEBUG_LOG', true);</div>";
}

// Test WebP path fixing
echo "<h2>🧪 Testing WebP Path Fixing</h2>";

global $wpdb;

// Get one converted image for testing
$converted_image = $wpdb->get_row("
    SELECT p.ID, p.post_title, pm.meta_value
    FROM {$wpdb->posts} p
    INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
    WHERE p.post_type = 'attachment'
    AND pm.meta_key = '_webp_conversion_data'
    AND pm.meta_value LIKE '%\"converted\":true%'
    LIMIT 1
");

if (!$converted_image) {
    echo "<p class='error'>❌ No converted images found for testing!</p>";
    echo "<p>Please convert some images first using the bulk conversion tool.</p>";
    exit;
}

echo "<h3>Testing with: {$converted_image->post_title} (ID: {$converted_image->ID})</h3>";

$conversion_data = maybe_unserialize($converted_image->meta_value);

if (!is_array($conversion_data) || !isset($conversion_data['webp_path'])) {
    echo "<p class='error'>❌ Invalid conversion data!</p>";
    exit;
}

$stored_webp_path = $conversion_data['webp_path'];
echo "<p><strong>Stored WebP Path:</strong> <code>{$stored_webp_path}</code></p>";

// Test the path as stored
$exists_as_stored = file_exists($stored_webp_path);
echo "<p><strong>Exists as stored:</strong> " . ($exists_as_stored ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";

if (!$exists_as_stored) {
    echo "<h4>🔧 Testing Path Fixes</h4>";
    
    // Test wp_normalize_path
    $normalized_path = wp_normalize_path($stored_webp_path);
    echo "<p><strong>Normalized path:</strong> <code>{$normalized_path}</code></p>";
    $exists_normalized = file_exists($normalized_path);
    echo "<p><strong>Exists normalized:</strong> " . ($exists_normalized ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
    
    // Test manual slash fixes
    $forward_slash_path = str_replace('\\', '/', $stored_webp_path);
    echo "<p><strong>Forward slash path:</strong> <code>{$forward_slash_path}</code></p>";
    $exists_forward = file_exists($forward_slash_path);
    echo "<p><strong>Exists forward:</strong> " . ($exists_forward ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
    
    // Test regenerating path from original file
    $original_file = get_attached_file($converted_image->ID);
    echo "<p><strong>Original file:</strong> <code>{$original_file}</code></p>";
    
    if ($original_file && file_exists($original_file)) {
        // Load WebP class to test path generation
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once('class-smart-webp-conversion.php');
        }
        
        if (class_exists('Redco_Smart_WebP_Conversion')) {
            $webp_instance = new Redco_Smart_WebP_Conversion();
            
            // Use reflection to access private method
            $reflection = new ReflectionClass($webp_instance);
            $get_webp_path_method = $reflection->getMethod('get_webp_path');
            $get_webp_path_method->setAccessible(true);
            
            $regenerated_path = $get_webp_path_method->invoke($webp_instance, $original_file);
            echo "<p><strong>Regenerated path:</strong> <code>{$regenerated_path}</code></p>";
            
            $exists_regenerated = file_exists($regenerated_path);
            echo "<p><strong>Exists regenerated:</strong> " . ($exists_regenerated ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
            
            if ($exists_regenerated && $regenerated_path !== $stored_webp_path) {
                echo "<p class='warning'>⚠️ <strong>PATH MISMATCH DETECTED!</strong></p>";
                echo "<p>The stored path in the database is incorrect. The correct path should be:</p>";
                echo "<div class='code'>{$regenerated_path}</div>";
                echo "<p>This explains why Media Library thumbnails are broken!</p>";
                
                // Test URL filtering with the correct path
                echo "<h4>🧪 Testing URL Filtering with Correct Path</h4>";
                
                // Simulate URL filtering
                $upload_dir = wp_upload_dir();
                $correct_webp_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $regenerated_path);
                $correct_webp_url = wp_normalize_path($correct_webp_url);
                $correct_webp_url = str_replace('\\', '/', $correct_webp_url);
                
                echo "<p><strong>Correct WebP URL:</strong> <a href='{$correct_webp_url}' target='_blank'>{$correct_webp_url}</a></p>";
                
                // Test if URL is accessible
                $headers = @get_headers($correct_webp_url);
                $url_accessible = $headers && strpos($headers[0], '200') !== false;
                echo "<p><strong>URL accessible:</strong> " . ($url_accessible ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
            }
        }
    }
}

// Test actual URL filtering
echo "<h3>🔗 Testing Live URL Filtering</h3>";

// Test wp_get_attachment_url
$current_url = wp_get_attachment_url($converted_image->ID);
echo "<p><strong>Current wp_get_attachment_url:</strong> {$current_url}</p>";

$has_webp_extension = strpos($current_url, '.webp') !== false;
echo "<p><strong>URL contains .webp:</strong> " . ($has_webp_extension ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";

if (!$has_webp_extension) {
    echo "<p class='error'>❌ URL filtering is not working! This is why Media Library shows broken images.</p>";
} else {
    echo "<p class='success'>✅ URL filtering is working! Media Library should display WebP images.</p>";
}

echo "<h3>📋 Summary & Next Steps</h3>";

if (!$exists_as_stored) {
    echo "<p class='error'><strong>ROOT CAUSE IDENTIFIED:</strong> WebP file paths in database are incorrect!</p>";
    echo "<p><strong>Solutions:</strong></p>";
    echo "<ol>";
    echo "<li><strong>Immediate Fix:</strong> The path fixing code I added should resolve this automatically</li>";
    echo "<li><strong>Long-term Fix:</strong> Re-run bulk conversion to update database with correct paths</li>";
    echo "<li><strong>Verification:</strong> Refresh Media Library page to see if thumbnails now work</li>";
    echo "</ol>";
} else {
    echo "<p class='success'>✅ WebP file paths appear to be correct.</p>";
    echo "<p>If Media Library still shows broken images, the issue may be with hook registration or URL generation.</p>";
}

echo "<h3>🔍 Debug Log Location</h3>";
echo "<p>If WP_DEBUG_LOG is enabled, check these locations for detailed debug logs:</p>";
echo "<ul>";
echo "<li><code>/wp-content/debug.log</code></li>";
echo "<li><code>/wp-content/redco-webp-errors.log</code></li>";
echo "</ul>";
echo "<p>Look for messages starting with <code>🖼️ MEDIA_LIBRARY_DEBUG:</code> and <code>🖼️ JS_DEBUG:</code></p>";
