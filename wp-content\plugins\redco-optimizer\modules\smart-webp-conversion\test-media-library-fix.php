<?php
/**
 * Test Media Library WebP Fix
 * 
 * This script tests the complete fix for Media Library WebP display issues
 */

// Load WordPress
require_once('../../../../../../wp-load.php');

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🧪 Media Library WebP Fix Test</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .warning { color: orange; } .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }</style>";

// 1. Check if syntax errors are fixed
echo "<h2>1. Syntax Error Check</h2>";
if (class_exists('Redco_Smart_WebP_Conversion')) {
    echo "<p class='success'>✅ WebP class loads without syntax errors</p>";
    
    try {
        $webp_instance = new Redco_Smart_WebP_Conversion();
        echo "<p class='success'>✅ WebP instance created successfully</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ Failed to create WebP instance: " . $e->getMessage() . "</p>";
        exit;
    }
} else {
    echo "<p class='error'>❌ WebP class not available - syntax errors may exist</p>";
    exit;
}

// 2. Test conversion with new URL storage
echo "<h2>2. Test Conversion with URL Storage</h2>";

global $wpdb;

// Find one unconverted image
$test_image = $wpdb->get_row("
    SELECT p.ID, p.post_title
    FROM {$wpdb->posts} p
    LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
    WHERE p.post_type = 'attachment'
    AND p.post_mime_type IN ('image/jpeg', 'image/png', 'image/gif')
    AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
    LIMIT 1
");

if ($test_image) {
    echo "<p>Testing with: {$test_image->post_title} (ID: {$test_image->ID})</p>";
    
    $file_path = get_attached_file($test_image->ID);
    if ($file_path && file_exists($file_path)) {
        echo "<p>File path: {$file_path}</p>";
        
        try {
            echo "<p>🔄 Converting image...</p>";
            $result = $webp_instance->convert_image_to_webp($test_image->ID);
            
            if ($result['success']) {
                echo "<p class='success'>✅ Conversion successful!</p>";
                
                // Check the stored conversion data
                $conversion_data = get_post_meta($test_image->ID, '_webp_conversion_data', true);
                
                echo "<h3>Stored Conversion Data:</h3>";
                echo "<div class='code'>";
                echo "webp_path: " . (isset($conversion_data['webp_path']) ? $conversion_data['webp_path'] : 'NOT SET') . "\n";
                echo "webp_url: " . (isset($conversion_data['webp_url']) ? $conversion_data['webp_url'] : 'NOT SET') . "\n";
                echo "webp_sizes count: " . (isset($conversion_data['webp_sizes']) ? count($conversion_data['webp_sizes']) : 0) . "\n";
                echo "webp_size_urls count: " . (isset($conversion_data['webp_size_urls']) ? count($conversion_data['webp_size_urls']) : 0) . "\n";
                echo "</div>";
                
                // Test URL filtering
                echo "<h3>URL Filtering Test:</h3>";
                $original_url = wp_get_attachment_url($test_image->ID);
                echo "<p>wp_get_attachment_url: {$original_url}</p>";
                
                $has_webp = strpos($original_url, '.webp') !== false;
                echo "<p>Contains .webp: " . ($has_webp ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
                
                if ($has_webp) {
                    echo "<p class='success'>🎉 URL filtering is working! Media Library should display WebP images.</p>";
                    
                    // Test if URL is accessible
                    $headers = @get_headers($original_url);
                    $url_accessible = $headers && strpos($headers[0], '200') !== false;
                    echo "<p>URL accessible: " . ($url_accessible ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
                    
                    if ($url_accessible) {
                        echo "<p class='success'>🎉 Perfect! WebP URL is accessible. Media Library thumbnails should work!</p>";
                    }
                } else {
                    echo "<p class='error'>❌ URL filtering not working. Check hook registration.</p>";
                }
                
                // Test JavaScript data
                echo "<h3>JavaScript Data Test:</h3>";
                $attachment_obj = get_post($test_image->ID);
                $js_data = wp_prepare_attachment_for_js($attachment_obj);
                
                if ($js_data && isset($js_data['url'])) {
                    echo "<p>JavaScript URL: {$js_data['url']}</p>";
                    $js_has_webp = strpos($js_data['url'], '.webp') !== false;
                    echo "<p>JavaScript URL has .webp: " . ($js_has_webp ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
                }
                
            } else {
                echo "<p class='error'>❌ Conversion failed: {$result['error']}</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Conversion error: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='error'>❌ File not found: {$file_path}</p>";
    }
} else {
    echo "<p class='warning'>⚠️ No unconverted images found. Testing with existing converted image...</p>";
    
    // Test with existing converted image
    $converted_image = $wpdb->get_row("
        SELECT p.ID, p.post_title
        FROM {$wpdb->posts} p
        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'attachment'
        AND pm.meta_key = '_webp_conversion_data'
        AND pm.meta_value LIKE '%\"converted\":true%'
        LIMIT 1
    ");
    
    if ($converted_image) {
        echo "<p>Testing with existing converted image: {$converted_image->post_title} (ID: {$converted_image->ID})</p>";
        
        // Check conversion data format
        $conversion_data = get_post_meta($converted_image->ID, '_webp_conversion_data', true);
        
        echo "<h3>Existing Conversion Data:</h3>";
        echo "<div class='code'>";
        echo "webp_path: " . (isset($conversion_data['webp_path']) ? $conversion_data['webp_path'] : 'NOT SET') . "\n";
        echo "webp_url: " . (isset($conversion_data['webp_url']) ? $conversion_data['webp_url'] : 'NOT SET') . "\n";
        echo "webp_sizes count: " . (isset($conversion_data['webp_sizes']) ? count($conversion_data['webp_sizes']) : 0) . "\n";
        echo "webp_size_urls count: " . (isset($conversion_data['webp_size_urls']) ? count($conversion_data['webp_size_urls']) : 0) . "\n";
        echo "</div>";
        
        // Test URL filtering
        echo "<h3>URL Filtering Test:</h3>";
        $current_url = wp_get_attachment_url($converted_image->ID);
        echo "<p>Current URL: {$current_url}</p>";
        
        $has_webp = strpos($current_url, '.webp') !== false;
        echo "<p>Contains .webp: " . ($has_webp ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
        
        if (!$has_webp && isset($conversion_data['webp_url'])) {
            echo "<p class='warning'>⚠️ URL filtering not working, but webp_url is stored: {$conversion_data['webp_url']}</p>";
        }
    } else {
        echo "<p class='error'>❌ No converted images found at all!</p>";
    }
}

// 3. Summary and recommendations
echo "<h2>3. Summary & Recommendations</h2>";

echo "<h3>✅ Fixes Applied:</h3>";
echo "<ul>";
echo "<li><strong>Syntax Error Fixed:</strong> Missing closing brace and method added</li>";
echo "<li><strong>URL Storage:</strong> WebP URLs are now stored in database alongside file paths</li>";
echo "<li><strong>Path Format Fixing:</strong> Comprehensive path normalization and slash fixing</li>";
echo "<li><strong>URL Filtering Enhanced:</strong> Uses stored URLs directly instead of converting paths</li>";
echo "<li><strong>Fallback Support:</strong> Handles old conversion data with only file paths</li>";
echo "</ul>";

echo "<h3>🔧 Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Convert Images:</strong> Use the bulk conversion tool to convert your images with the new URL storage</li>";
echo "<li><strong>Test Media Library:</strong> Refresh the Media Library page and check if thumbnails display correctly</li>";
echo "<li><strong>Enable Debug:</strong> Add <code>define('WP_DEBUG', true);</code> to wp-config.php to see detailed logs</li>";
echo "<li><strong>Check Error Logs:</strong> Look for messages starting with <code>🖼️ MEDIA_LIBRARY_DEBUG:</code></li>";
echo "</ol>";

echo "<h3>🎯 Expected Results:</h3>";
echo "<ul>";
echo "<li>✅ Media Library thumbnails display WebP images correctly</li>";
echo "<li>✅ Image URLs end with <code>.webp</code> for converted images</li>";
echo "<li>✅ No more broken image icons in Media Library</li>";
echo "<li>✅ JavaScript receives correct WebP URLs</li>";
echo "<li>✅ All thumbnail sizes use WebP URLs</li>";
echo "</ul>";

echo "<p class='success'><strong>The comprehensive fix is now in place. Convert some images and test the Media Library!</strong></p>";
