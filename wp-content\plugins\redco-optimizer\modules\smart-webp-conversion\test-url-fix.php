<?php
/**
 * Test URL Fix - Verify the local path to URL conversion works
 */

// Load WordPress
require_once('../../../../../../wp-load.php');

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🔧 Test URL Fix for Local File Paths</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .warning { color: orange; } .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; }</style>";

// Test the specific image from the screenshot
$attachment_id = 15; // Adjust this to match the ID from your screenshot

echo "<h2>Testing Attachment ID: {$attachment_id}</h2>";

// 1. Check if attachment exists
$attachment = get_post($attachment_id);
if (!$attachment) {
    echo "<p class='error'>❌ Attachment ID {$attachment_id} not found. Please check the ID in your Media Library URL.</p>";
    exit;
}

echo "<p>Image Title: {$attachment->post_title}</p>";

// 2. Test WordPress core URL function
echo "<h3>WordPress Core URL Test:</h3>";
$core_url = wp_get_attachment_url($attachment_id);
echo "<p><strong>wp_get_attachment_url result:</strong> {$core_url}</p>";

// Check if it's a local path
$is_local_path = (strpos($core_url, 'D:') === 0 || strpos($core_url, 'C:') === 0 || 
                 (strpos($core_url, '/') === 0 && strpos($core_url, 'http') !== 0));

echo "<p>Is local path: " . ($is_local_path ? "<span class='error'>❌ YES - This is the problem!</span>" : "<span class='success'>✅ NO</span>") . "</p>";

// 3. Test our WebP instance
$webp_instance = $GLOBALS['redco_webp_instance'] ?? null;
if (!$webp_instance) {
    echo "<p class='error'>❌ WebP instance not found</p>";
    exit;
}

// 4. Test URL filtering manually
echo "<h3>Manual URL Filtering Test:</h3>";

if (method_exists($webp_instance, 'serve_webp_url_in_media_library')) {
    echo "<p>Testing serve_webp_url_in_media_library method...</p>";
    
    $filtered_url = $webp_instance->serve_webp_url_in_media_library($core_url, $attachment_id);
    
    echo "<div class='code'>";
    echo "Input URL: {$core_url}\n";
    echo "Filtered URL: {$filtered_url}\n";
    echo "</div>";
    
    // Check if the filtering fixed the local path issue
    $filtered_is_url = (strpos($filtered_url, 'http') === 0);
    $filtering_worked = ($core_url !== $filtered_url);
    
    echo "<p>Filtering worked: " . ($filtering_worked ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
    echo "<p>Result is web URL: " . ($filtered_is_url ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
    
    if ($is_local_path && $filtered_is_url) {
        echo "<p class='success'>🎉 SUCCESS! Local path was converted to web URL!</p>";
    } else if ($is_local_path && !$filtered_is_url) {
        echo "<p class='error'>❌ FAILED! Local path was not converted to web URL.</p>";
    } else if (!$is_local_path && $filtered_is_url) {
        echo "<p class='success'>✅ GOOD! URL was already correct and remains correct.</p>";
    }
    
} else {
    echo "<p class='error'>❌ Method serve_webp_url_in_media_library not found</p>";
}

// 5. Test JavaScript data
echo "<h3>JavaScript Data Test:</h3>";

if (method_exists($webp_instance, 'fix_media_library_webp_urls')) {
    echo "<p>Testing fix_media_library_webp_urls method...</p>";
    
    // Simulate the response that would come from wp_prepare_attachment_for_js
    $test_response = array(
        'type' => 'image',
        'url' => $core_url,
        'id' => $attachment_id
    );
    
    $fixed_response = $webp_instance->fix_media_library_webp_urls($test_response, $attachment, array());
    
    echo "<div class='code'>";
    echo "Input response URL: {$test_response['url']}\n";
    echo "Fixed response URL: {$fixed_response['url']}\n";
    echo "</div>";
    
    $js_fixed_is_url = (strpos($fixed_response['url'], 'http') === 0);
    $js_filtering_worked = ($test_response['url'] !== $fixed_response['url']);
    
    echo "<p>JS filtering worked: " . ($js_filtering_worked ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
    echo "<p>JS result is web URL: " . ($js_fixed_is_url ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
    
} else {
    echo "<p class='error'>❌ Method fix_media_library_webp_urls not found</p>";
}

// 6. Test conversion and URL storage
echo "<h3>Conversion Data Test:</h3>";

$conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
if ($conversion_data) {
    echo "<p>Image has conversion data:</p>";
    echo "<ul>";
    echo "<li>Converted: " . (isset($conversion_data['converted']) && $conversion_data['converted'] ? "✅ YES" : "❌ NO") . "</li>";
    echo "<li>webp_url stored: " . (isset($conversion_data['webp_url']) ? "✅ YES" : "❌ NO") . "</li>";
    echo "<li>webp_path stored: " . (isset($conversion_data['webp_path']) ? "✅ YES" : "❌ NO") . "</li>";
    echo "</ul>";
    
    if (isset($conversion_data['webp_url'])) {
        echo "<p><strong>Stored WebP URL:</strong> {$conversion_data['webp_url']}</p>";
        $stored_is_url = (strpos($conversion_data['webp_url'], 'http') === 0);
        echo "<p>Stored URL is web URL: " . ($stored_is_url ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
    }
} else {
    echo "<p class='warning'>⚠️ No conversion data found. Convert this image to test the new URL storage.</p>";
}

// 7. Summary and recommendations
echo "<h2>🎯 Summary</h2>";

if ($is_local_path) {
    echo "<p class='error'><strong>CONFIRMED ISSUE:</strong> WordPress is serving local file paths instead of URLs!</p>";
    echo "<p><strong>ROOT CAUSE:</strong> WordPress core metadata is corrupted or misconfigured.</p>";
    
    if ($filtered_is_url) {
        echo "<p class='success'><strong>GOOD NEWS:</strong> Our URL filtering fix is working and converts local paths to URLs!</p>";
    } else {
        echo "<p class='error'><strong>PROBLEM:</strong> Our URL filtering is not working properly.</p>";
    }
} else {
    echo "<p class='success'>✅ WordPress is serving proper URLs (no local path issue detected)</p>";
}

echo "<h3>🔧 Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Enable Debug Logging:</strong> Add <code>define('WP_DEBUG', true);</code> to wp-config.php</li>";
echo "<li><strong>Refresh Media Library:</strong> Go back to the Media Library and refresh the page</li>";
echo "<li><strong>Check Error Logs:</strong> Look for messages starting with <code>🖼️ MEDIA_LIBRARY_DEBUG:</code></li>";
echo "<li><strong>Test Image Edit:</strong> Click on the image in Media Library to see if the File URL is now correct</li>";
echo "</ol>";

echo "<p class='success'><strong>The fix should now convert local file paths to proper web URLs automatically!</strong></p>";
