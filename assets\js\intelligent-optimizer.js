/**
 * Intelligent Optimizer JavaScript
 * Handles the intelligent optimization wizard interface
 */

(function($) {
    'use strict';

    // Intelligent Optimizer object
    window.RedcoIntelligentOptimizer = {
        
        // Current analysis data
        analysisData: null,
        recommendations: null,
        selectedRecommendations: [],
        
        // Initialize the optimizer
        init: function() {
            this.bindEvents();
        },
        
        // Bind event handlers
        bindEvents: function() {
            // Use more specific event binding to avoid conflicts
            $(document).off('click.intelligent-optimizer').on('click.intelligent-optimizer', '#run-intelligent-optimizer', this.runOptimizer.bind(this));
            $(document).on('click', '.recommendation-checkbox', this.toggleRecommendation.bind(this));
            $(document).on('click', '#preview-optimizations', this.previewOptimizations.bind(this));
            $(document).on('click', '#apply-optimizations', this.applyOptimizations.bind(this));
            $(document).on('click', '#close-optimizer-modal', this.closeModal.bind(this));
            $(document).on('click', '.optimizer-modal-overlay', this.closeModal.bind(this));
        },
        
        // Run the intelligent optimizer
        runOptimizer: function(e) {
            e.preventDefault();
            e.stopPropagation();

            const button = $(e.target);
            const originalText = button.text();

            // Show loading state
            button.prop('disabled', true)
                  .html('<span class="spinner is-active" style="float: none; margin-right: 8px;"></span>Analyzing...');

            // Store reference for cleanup
            const self = this;

            // Check if AJAX data is available
            const ajaxData = window.redcoOptimizerAjax || window.redcoAjax;
            if (!ajaxData) {
                this.showError('Configuration error: AJAX data not available. Please refresh the page.');
                button.prop('disabled', false).text(originalText);
                return;
            }

            // Make AJAX request with timeout
            $.ajax({
                url: ajaxData.ajaxurl,
                type: 'POST',
                timeout: 60000, // 60 second timeout
                data: {
                    action: 'redco_run_intelligent_optimizer',
                    nonce: ajaxData.nonce
                },
                success: function(response) {
                    self.handleAnalysisSuccess(response);
                },
                error: function(xhr, status, error) {
                    self.handleAnalysisError(xhr, status, error);
                },
                complete: function() {
                    // Always reset button state
                    button.prop('disabled', false).text(originalText);
                }
            });
        },
        
        // Handle successful analysis
        handleAnalysisSuccess: function(response) {
            if (response.success) {
                this.analysisData = response.data.analysis;
                this.recommendations = response.data.recommendations;
                this.selectedRecommendations = [];

                this.showOptimizerModal(response.data);
            } else {
                this.showError('Analysis failed: ' + (response.data || 'Unknown error'));
            }
        },
        
        // Handle analysis error
        handleAnalysisError: function(xhr, status, error) {
            let errorMessage = 'Analysis failed: ';

            if (status === 'timeout') {
                errorMessage += 'Request timed out. Please try again.';
            } else if (xhr.responseJSON && xhr.responseJSON.data) {
                errorMessage += xhr.responseJSON.data;
            } else if (error) {
                errorMessage += error;
            } else {
                errorMessage += 'Unknown error occurred. Please refresh the page and try again.';
            }

            this.showError(errorMessage);
        },
        
        // Show the optimizer modal
        showOptimizerModal: function(data) {
            const modal = this.createOptimizerModal(data);
            $('body').append(modal);
            
            // Animate in
            setTimeout(function() {
                $('.optimizer-modal').addClass('active');
            }, 10);
        },
        
        // Create the optimizer modal HTML
        createOptimizerModal: function(data) {
            const performance = data.performance_score;
            const recommendations = data.recommendations;
            const improvement = data.estimated_improvement;
            
            return `
                <div class="optimizer-modal-overlay">
                    <div class="optimizer-modal">
                        <div class="optimizer-modal-header">
                            <h2><span class="dashicons dashicons-performance"></span> Intelligent Optimizer</h2>
                            <button type="button" class="optimizer-modal-close" id="close-optimizer-modal">
                                <span class="dashicons dashicons-no-alt"></span>
                            </button>
                        </div>
                        
                        <div class="optimizer-modal-content">
                            <!-- Performance Score Section -->
                            <div class="optimizer-section performance-score-section">
                                <h3>Current Performance Analysis</h3>
                                <div class="performance-score-display">
                                    <div class="score-circle grade-${performance.grade.toLowerCase().replace('+', 'plus')}">
                                        <div class="score-value">${performance.total}</div>
                                        <div class="score-grade">${performance.grade}</div>
                                    </div>
                                    <div class="score-details">
                                        <div class="score-breakdown">
                                            <div class="score-item">
                                                <span class="score-label">Server Capabilities:</span>
                                                <span class="score-value">${performance.factors.server}/20</span>
                                            </div>
                                            <div class="score-item">
                                                <span class="score-label">Active Optimizations:</span>
                                                <span class="score-value">${performance.factors.optimizations}/50</span>
                                            </div>
                                            <div class="score-item">
                                                <span class="score-label">Performance Metrics:</span>
                                                <span class="score-value">${performance.factors.performance}/20</span>
                                            </div>
                                            <div class="score-item">
                                                <span class="score-label">Plugin Compatibility:</span>
                                                <span class="score-value">${performance.factors.conflicts}/10</span>
                                            </div>
                                        </div>
                                        <div class="modules-status">
                                            <span>${performance.enabled_modules}/${performance.total_modules} optimization modules active</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Recommendations Section -->
                            <div class="optimizer-section recommendations-section">
                                <h3>Optimization Recommendations</h3>
                                <div class="recommendations-list">
                                    ${this.generateRecommendationsHTML(recommendations)}
                                </div>
                            </div>
                            
                            <!-- Estimated Improvement Section -->
                            <div class="optimizer-section improvement-section">
                                <h3>Expected Results</h3>
                                <div class="improvement-display">
                                    <div class="improvement-percentage">
                                        <span class="improvement-value">+${improvement.percentage}%</span>
                                        <span class="improvement-label">Performance Improvement</span>
                                    </div>
                                    <div class="improvement-description">
                                        <p>${improvement.description}</p>
                                        <p class="improvement-timeframe">Changes take effect ${improvement.timeframe}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="optimizer-modal-footer">
                            <div class="optimizer-actions">
                                <button type="button" class="button button-secondary" id="preview-optimizations">
                                    <span class="dashicons dashicons-visibility"></span>
                                    Preview Changes
                                </button>
                                <button type="button" class="button button-primary" id="apply-optimizations">
                                    <span class="dashicons dashicons-yes-alt"></span>
                                    Apply Selected Optimizations
                                </button>
                            </div>
                            <div class="selected-count">
                                <span id="selected-recommendations-count">0</span> recommendations selected
                            </div>
                        </div>
                    </div>
                </div>
            `;
        },
        
        // Generate recommendations HTML
        generateRecommendationsHTML: function(recommendations) {
            let html = '';
            
            recommendations.forEach((rec, index) => {
                const priorityClass = rec.priority;
                const impactClass = rec.impact;
                const requirementsMet = rec.requirements_met;
                const disabled = !requirementsMet ? 'disabled' : '';
                
                html += `
                    <div class="recommendation-item priority-${priorityClass} impact-${impactClass} ${disabled}">
                        <div class="recommendation-header">
                            <label class="recommendation-checkbox-label">
                                <input type="checkbox" 
                                       class="recommendation-checkbox" 
                                       data-rec-id="${rec.id}"
                                       data-rec-index="${index}"
                                       ${disabled}
                                       ${requirementsMet ? '' : 'disabled'}>
                                <span class="recommendation-title">${rec.title}</span>
                            </label>
                            <div class="recommendation-badges">
                                <span class="priority-badge priority-${priorityClass}">${rec.priority}</span>
                                <span class="impact-badge impact-${impactClass}">${rec.impact} impact</span>
                                <span class="improvement-badge">+${rec.estimated_improvement}</span>
                            </div>
                        </div>
                        <div class="recommendation-content">
                            <p class="recommendation-description">${rec.description}</p>
                            <p class="recommendation-reason"><strong>Why:</strong> ${rec.reason}</p>
                            ${!requirementsMet ? '<p class="recommendation-warning"><span class="dashicons dashicons-warning"></span> Requirements not met for this optimization</p>' : ''}
                        </div>
                    </div>
                `;
            });
            
            return html;
        },
        
        // Toggle recommendation selection
        toggleRecommendation: function(e) {
            const checkbox = $(e.target);
            const recId = checkbox.data('rec-id');
            const recIndex = checkbox.data('rec-index');
            
            if (checkbox.is(':checked')) {
                if (this.selectedRecommendations.indexOf(recId) === -1) {
                    this.selectedRecommendations.push(recId);
                }
            } else {
                const index = this.selectedRecommendations.indexOf(recId);
                if (index > -1) {
                    this.selectedRecommendations.splice(index, 1);
                }
            }
            
            this.updateSelectedCount();
            this.updateActionButtons();
        },
        
        // Update selected recommendations count
        updateSelectedCount: function() {
            $('#selected-recommendations-count').text(this.selectedRecommendations.length);
        },
        
        // Update action buttons state
        updateActionButtons: function() {
            const hasSelected = this.selectedRecommendations.length > 0;
            $('#preview-optimizations, #apply-optimizations').prop('disabled', !hasSelected);
        },
        
        // Preview optimizations
        previewOptimizations: function(e) {
            e.preventDefault();
            
            if (this.selectedRecommendations.length === 0) {
                this.showError('Please select at least one recommendation to preview.');
                return;
            }
            
            const button = $(e.target);
            const originalText = button.text();
            
            button.prop('disabled', true)
                  .html('<span class="spinner is-active" style="float: none; margin-right: 8px;"></span>Generating Preview...');
            
            // Prepare recommendation data
            const recData = {};
            this.selectedRecommendations.forEach(recId => {
                const rec = this.recommendations.find(r => r.id === recId);
                if (rec) {
                    recData[recId] = rec;
                }
            });
            
            const ajaxData = window.redcoOptimizerAjax || window.redcoAjax;

            $.ajax({
                url: ajaxData.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_optimization_preview',
                    nonce: ajaxData.nonce,
                    recommendations: this.selectedRecommendations,
                    rec_data: recData
                },
                success: this.handlePreviewSuccess.bind(this),
                error: this.handlePreviewError.bind(this),
                complete: function() {
                    button.prop('disabled', false).text(originalText);
                }
            });
        },
        
        // Handle preview success
        handlePreviewSuccess: function(response) {
            if (response.success) {
                this.showPreviewModal(response.data);
            } else {
                this.showError('Preview failed: ' + (response.data || 'Unknown error'));
            }
        },
        
        // Handle preview error
        handlePreviewError: function(xhr, status, error) {
            this.showError('Preview failed: ' + error);
        },
        
        // Show error message
        showError: function(message) {
            // Create or update error notice
            let notice = $('.optimizer-error-notice');
            if (notice.length === 0) {
                notice = $('<div class="notice notice-error optimizer-error-notice"><p></p></div>');
                $('.optimizer-modal-content').prepend(notice);
            }
            
            notice.find('p').text(message);
            notice.show();
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                notice.fadeOut();
            }, 5000);
        },
        
        // Show preview modal
        showPreviewModal: function(previewData) {
            const modal = this.createPreviewModal(previewData);
            $('body').append(modal);

            setTimeout(function() {
                $('.preview-modal').addClass('active');
            }, 10);
        },

        // Create preview modal HTML
        createPreviewModal: function(data) {
            const current = data.current_score;
            const projected = data.projected_score;
            const improvement = data.improvement;
            const summary = data.summary;

            return `
                <div class="preview-modal-overlay">
                    <div class="preview-modal">
                        <div class="preview-modal-header">
                            <h2><span class="dashicons dashicons-visibility"></span> Optimization Preview</h2>
                            <button type="button" class="preview-modal-close">
                                <span class="dashicons dashicons-no-alt"></span>
                            </button>
                        </div>

                        <div class="preview-modal-content">
                            <div class="score-comparison">
                                <div class="score-before">
                                    <h4>Current Score</h4>
                                    <div class="score-circle grade-${current.grade.toLowerCase().replace('+', 'plus')}">
                                        <div class="score-value">${current.total}</div>
                                        <div class="score-grade">${current.grade}</div>
                                    </div>
                                </div>

                                <div class="score-arrow">
                                    <span class="dashicons dashicons-arrow-right-alt"></span>
                                    <div class="improvement-indicator">
                                        <span class="improvement-value">+${improvement}</span>
                                    </div>
                                </div>

                                <div class="score-after">
                                    <h4>Projected Score</h4>
                                    <div class="score-circle grade-${projected.grade.toLowerCase().replace('+', 'plus')}">
                                        <div class="score-value">${projected.total}</div>
                                        <div class="score-grade">${projected.grade}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="preview-summary">
                                <h4>Changes Summary</h4>
                                <ul class="changes-list">
                                    ${data.changes.map(change => `
                                        <li class="change-item">
                                            <span class="dashicons dashicons-yes-alt"></span>
                                            ${change.description}
                                        </li>
                                    `).join('')}
                                </ul>

                                <div class="summary-stats">
                                    <div class="stat-item">
                                        <span class="stat-value">${summary.modules_to_enable}</span>
                                        <span class="stat-label">Modules to Enable</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">${summary.estimated_improvement}</span>
                                        <span class="stat-label">Score Improvement</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">${summary.new_grade}</span>
                                        <span class="stat-label">New Grade</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="preview-modal-footer">
                            <button type="button" class="button button-secondary preview-modal-close">Cancel</button>
                            <button type="button" class="button button-primary" id="confirm-apply-optimizations">
                                <span class="dashicons dashicons-yes-alt"></span>
                                Apply These Optimizations
                            </button>
                        </div>
                    </div>
                </div>
            `;
        },

        // Apply optimizations
        applyOptimizations: function(e) {
            e.preventDefault();

            if (this.selectedRecommendations.length === 0) {
                this.showError('Please select at least one recommendation to apply.');
                return;
            }

            const button = $(e.target);
            const originalText = button.text();

            button.prop('disabled', true)
                  .html('<span class="spinner is-active" style="float: none; margin-right: 8px;"></span>Applying Optimizations...');

            // Prepare recommendation data
            const recData = {};
            this.selectedRecommendations.forEach(recId => {
                const rec = this.recommendations.find(r => r.id === recId);
                if (rec) {
                    recData[recId] = rec;
                }
            });

            const ajaxData = window.redcoOptimizerAjax || window.redcoAjax;

            $.ajax({
                url: ajaxData.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_apply_optimizer_recommendations',
                    nonce: ajaxData.nonce,
                    recommendations: this.selectedRecommendations,
                    rec_data: recData
                },
                success: this.handleApplySuccess.bind(this),
                error: this.handleApplyError.bind(this),
                complete: function() {
                    button.prop('disabled', false).text(originalText);
                }
            });
        },

        // Handle apply success
        handleApplySuccess: function(response) {
            if (response.success) {
                this.showSuccessModal(response.data);
            } else {
                this.showError('Failed to apply optimizations: ' + (response.data || 'Unknown error'));
            }
        },

        // Handle apply error
        handleApplyError: function(xhr, status, error) {
            this.showError('Failed to apply optimizations: ' + error);
        },

        // Show success modal
        showSuccessModal: function(data) {
            const modal = this.createSuccessModal(data);
            $('body').append(modal);

            setTimeout(function() {
                $('.success-modal').addClass('active');
            }, 10);
        },

        // Create success modal HTML
        createSuccessModal: function(data) {
            const newScore = data.new_performance_score;
            const appliedCount = data.applied.length;
            const errorCount = data.errors.length;

            return `
                <div class="success-modal-overlay">
                    <div class="success-modal">
                        <div class="success-modal-header">
                            <h2><span class="dashicons dashicons-yes-alt"></span> Optimization Complete!</h2>
                        </div>

                        <div class="success-modal-content">
                            <div class="success-summary">
                                <div class="success-icon">
                                    <span class="dashicons dashicons-yes-alt"></span>
                                </div>
                                <div class="success-message">
                                    <h3>Successfully Applied ${appliedCount} Optimizations</h3>
                                    <p>${data.message}</p>
                                </div>
                            </div>

                            <div class="new-score-display">
                                <h4>Your New Performance Score</h4>
                                <div class="score-circle grade-${newScore.grade.toLowerCase().replace('+', 'plus')}">
                                    <div class="score-value">${newScore.total}</div>
                                    <div class="score-grade">${newScore.grade}</div>
                                </div>
                                <p class="score-improvement">
                                    ${newScore.enabled_modules}/${newScore.total_modules} optimization modules now active
                                </p>
                            </div>

                            ${errorCount > 0 ? `
                                <div class="errors-section">
                                    <h4>Issues Encountered</h4>
                                    <ul class="errors-list">
                                        ${data.errors.map(error => `
                                            <li class="error-item">
                                                <span class="dashicons dashicons-warning"></span>
                                                ${error.message}
                                            </li>
                                        `).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>

                        <div class="success-modal-footer">
                            <button type="button" class="button button-primary" id="close-success-modal">
                                <span class="dashicons dashicons-yes-alt"></span>
                                Done
                            </button>
                        </div>
                    </div>
                </div>
            `;
        },

        // Close modal
        closeModal: function(e) {
            if (e.target === e.currentTarget || $(e.target).hasClass('optimizer-modal-close') || $(e.target).parent().hasClass('optimizer-modal-close')) {
                $('.optimizer-modal').removeClass('active');
                setTimeout(function() {
                    $('.optimizer-modal-overlay').remove();
                }, 300);
            }
        }
    };

    // Bind additional events for modal interactions
    $(document).on('click', '.preview-modal-close', function(e) {
        $('.preview-modal').removeClass('active');
        setTimeout(function() {
            $('.preview-modal-overlay').remove();
        }, 300);
    });

    $(document).on('click', '#confirm-apply-optimizations', function(e) {
        $('.preview-modal').removeClass('active');
        setTimeout(function() {
            $('.preview-modal-overlay').remove();
        }, 300);
        RedcoIntelligentOptimizer.applyOptimizations(e);
    });

    $(document).on('click', '#close-success-modal', function(e) {
        $('.success-modal').removeClass('active');
        setTimeout(function() {
            $('.success-modal-overlay').remove();
            // Reload the page to show updated settings
            window.location.reload();
        }, 300);
    });

    // Initialize when document is ready
    $(document).ready(function() {
        RedcoIntelligentOptimizer.init();

        // Add direct event handler as backup to override any catch-all handlers
        setTimeout(function() {
            const button = $('#run-intelligent-optimizer');
            if (button.length) {
                button.off('click').on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    RedcoIntelligentOptimizer.runOptimizer(e);
                });
            }
        }, 100);
    });

})(jQuery);
