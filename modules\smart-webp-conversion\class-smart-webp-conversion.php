<?php
/**
 * Smart WebP Conversion Module for Redco Optimizer
 *
 * Production-Ready WebP Conversion System
 *
 * Features:
 * - Bulletproof error handling and recovery
 * - Memory-efficient batch processing
 * - Real-time progress tracking
 * - Comprehensive backup system
 * - Advanced quality optimization
 * - Browser compatibility detection
 * - Detailed logging and diagnostics
 * - Performance monitoring
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Smart_WebP_Conversion {

    /**
     * Version for cache busting and compatibility
     */
    const VERSION = '2.0.0';

    /**
     * Supported image formats for WebP conversion
     */
    const SUPPORTED_FORMATS = array('jpg', 'jpeg', 'png', 'gif');

    /**
     * Maximum file size for conversion (in bytes) - 50MB
     */
    const MAX_FILE_SIZE = 52428800;

    /**
     * Batch processing size for bulk operations
     */
    const BATCH_SIZE = 5;

    /**
     * Memory limit threshold (80% of available memory)
     */
    const MEMORY_THRESHOLD = 0.8;

    /**
     * Default settings - loaded from centralized configuration
     */
    private $default_settings;

    /**
     * Current settings
     */
    private $settings;

    /**
     * Conversion statistics
     */
    private $stats = array();

    /**
     * Error log
     */
    private $errors = array();

    /**
     * Processing queue
     */
    private $queue = array();

    /**
     * Initialize the module
     */
    public function __construct() {
        try {
            // Load default settings from centralized configuration
            $this->default_settings = Redco_Config::get_module_defaults('smart-webp-conversion');

            $this->settings = $this->get_settings();
            $this->init_hooks();
            // REMOVED: $this->init_error_handling() was causing constructor to hang
            // $this->init_error_handling();
            // REMOVED: $this->log() call was causing constructor to hang
            // $this->log('Enhanced WebP Conversion Module initialized', 'info');
        } catch (Exception $e) {
            // Silent error handling
        } catch (Error $e) {
            // Silent error handling
        }
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Only initialize if module is enabled
        if (!redco_is_module_enabled('smart-webp-conversion')) {
            return;
        }

        // FIXED: Wait for complete upload including thumbnail generation
        add_action('wp_generate_attachment_metadata', array($this, 'auto_convert_after_complete_upload'), 20, 2);

        // Hook into image serving
        add_filter('wp_get_attachment_image_src', array($this, 'serve_webp_if_supported'), 10, 4);
        add_filter('the_content', array($this, 'replace_images_in_content'), 999);

        // ENHANCEMENT: Add support for additional content areas
        add_filter('widget_text', array($this, 'replace_images_in_content'), 999);
        add_filter('the_excerpt', array($this, 'replace_images_in_content'), 999);

        // CRITICAL FIX: Add Media Library URL filters for proper thumbnail display
        add_filter('wp_get_attachment_url', array($this, 'serve_webp_url_in_media_library'), 10, 2);
        add_filter('wp_get_attachment_thumb_url', array($this, 'serve_webp_url_in_media_library'), 10, 2);
        add_filter('wp_prepare_attachment_for_js', array($this, 'fix_media_library_webp_urls'), 10, 3);

        // ADDITIONAL FIX: Add more comprehensive URL filtering for Media Library
        add_filter('wp_get_attachment_image_url', array($this, 'serve_webp_url_in_media_library'), 10, 2);
        add_filter('wp_calculate_image_srcset', array($this, 'fix_webp_srcset'), 10, 5);

        // DEBUGGING: Add action to verify hooks are registered
        if (defined('WP_DEBUG') && WP_DEBUG) {
            add_action('admin_init', array($this, 'debug_hook_registration'));
        }

        // Keep only essential hooks for WebP conversion functionality

        // AJAX handlers - REMOVED to prevent conflicts with global handlers
        // The global handlers in class-smart-webp-conversion.php will handle AJAX calls
        // add_action('wp_ajax_redco_webp_enhanced_bulk_convert', array($this, 'ajax_enhanced_bulk_convert'));
        // add_action('wp_ajax_redco_webp_enhanced_test', array($this, 'ajax_enhanced_test'));
        // add_action('wp_ajax_redco_webp_enhanced_stats', array($this, 'ajax_enhanced_stats'));

        // Cleanup hooks
        add_action('wp_scheduled_delete', array($this, 'cleanup_orphaned_webp_files'));
        add_action('delete_attachment', array($this, 'delete_webp_versions'));

        // AJAX handlers for stats refresh
        add_action('wp_ajax_redco_webp_get_recent_conversions', array($this, 'ajax_get_recent_conversions'));

        // REAL-TIME STATS: Hook into media upload events for automatic statistics updates
        add_action('add_attachment', array($this, 'trigger_stats_refresh_on_upload'), 10, 1);
        add_action('wp_generate_attachment_metadata', array($this, 'trigger_stats_refresh_after_metadata'), 30, 2);

        // REAL-TIME STATS: AJAX handler for refreshing statistics
        add_action('wp_ajax_redco_webp_refresh_stats', array($this, 'ajax_refresh_stats'));
        add_action('wp_ajax_redco_webp_check_refresh_needed', array($this, 'ajax_check_refresh_needed'));

        // REMOVED: AJAX handler moved to global handlers to prevent conflicts
        // add_action('wp_ajax_redco_webp_get_processable_images', array($this, 'ajax_get_processable_images'));

        // CORE AJAX HANDLERS: Only register rollback (others handled by global handlers)
        add_action('wp_ajax_redco_webp_rollback', array($this, 'ajax_rollback_conversion'));
    }

    /**
     * Initialize comprehensive error handling
     */
    private function init_error_handling() {
        // Set custom error handler for WebP operations
        if ($this->settings['enable_logging']) {
            ini_set('log_errors', 1);
            ini_set('error_log', WP_CONTENT_DIR . '/redco-webp-errors.log');
        }
    }

    /**
     * Enhanced logging method using centralized error handler
     */
    private function log($message, $level = 'info') {
        // Use centralized error handling system
        switch ($level) {
            case 'error':
                Redco_Error_Handler::error($message, Redco_Error_Handler::CONTEXT_WEBP);
                break;
            case 'warning':
                Redco_Error_Handler::warning($message, Redco_Error_Handler::CONTEXT_WEBP);
                break;
            case 'info':
            default:
                Redco_Error_Handler::info($message, Redco_Error_Handler::CONTEXT_WEBP);
                break;
        }
    }

    /**
     * Get module settings with enhanced defaults and robust boolean handling
     */
    public function get_settings() {
        // CRITICAL: Use the same settings as the main module for consistency
        $main_settings = get_option('redco_optimizer_smart_webp_conversion', array());

        // CRITICAL: Robust boolean conversion for auto_convert_uploads
        $auto_convert_uploads = false; // Default to disabled for safety
        if (isset($main_settings['auto_convert_uploads'])) {
            $value = $main_settings['auto_convert_uploads'];
            // Handle various value types: true, 1, '1', 'true', 'on', 'yes'
            if (is_bool($value)) {
                $auto_convert_uploads = $value;
            } elseif (is_numeric($value)) {
                $auto_convert_uploads = (int)$value === 1;
            } elseif (is_string($value)) {
                $auto_convert_uploads = in_array(strtolower($value), ['1', 'true', 'on', 'yes']);
            }
        }

        // Map main module settings to enhanced settings with robust type handling
        $enhanced_settings = array(
            'auto_convert_uploads' => $auto_convert_uploads, // Use robust boolean conversion
            'quality' => isset($main_settings['quality']) ? (int)$main_settings['quality'] : $this->default_settings['quality'],
            'lossless' => isset($main_settings['lossless']) ? (bool)$main_settings['lossless'] : $this->default_settings['lossless'],
            'backup_original' => true, // Always backup original files for safety
            'replace_in_content' => isset($main_settings['replace_in_content']) ? (bool)$main_settings['replace_in_content'] : true,
            'batch_size' => isset($main_settings['batch_size']) ? (int)$main_settings['batch_size'] : 10,
        );

        // Merge with enhanced defaults
        $settings = wp_parse_args($enhanced_settings, $this->default_settings);

        return $settings;
    }

    /**
     * Enhanced server capability check
     */
    public function can_convert_webp() {
        static $can_convert = null;

        if ($can_convert !== null) {
            return $can_convert;
        }

        $checks = array(
            'gd_extension' => extension_loaded('gd'),
            'imagewebp_function' => function_exists('imagewebp'),
            'webp_support' => false,
            'memory_available' => $this->check_memory_availability(),
            'write_permissions' => $this->check_write_permissions()
        );

        // Check WebP support in GD
        if ($checks['gd_extension'] && $checks['imagewebp_function']) {
            $gd_info = gd_info();
            $checks['webp_support'] = isset($gd_info['WebP Support']) && $gd_info['WebP Support'];
        }

        $can_convert = $checks['gd_extension'] &&
                      $checks['imagewebp_function'] &&
                      $checks['webp_support'] &&
                      $checks['memory_available'] &&
                      $checks['write_permissions'];

        $this->log('WebP capability check: ' . ($can_convert ? 'PASSED' : 'FAILED'), 'info');
        $this->log('Capability details: ' . json_encode($checks), 'debug');

        return $can_convert;
    }

    /**
     * Check memory availability
     */
    private function check_memory_availability() {
        $memory_limit = $this->get_memory_limit();
        $memory_usage = memory_get_usage(true);
        $available_memory = $memory_limit - $memory_usage;

        // Need at least 64MB available for image processing
        return $available_memory > (64 * 1024 * 1024);
    }

    /**
     * Get memory limit in bytes
     */
    private function get_memory_limit() {
        $memory_limit = ini_get('memory_limit');

        if ($memory_limit == -1) {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($memory_limit, -1));
        $value = (int) $memory_limit;

        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Check write permissions
     */
    private function check_write_permissions() {
        $upload_dir = redco_safe_wp_upload_dir();
        return is_writable($upload_dir['basedir']);
    }

    /**
     * Enhanced image format detection
     */
    public function is_convertible_image($file_path) {
        if (!$file_path || !is_string($file_path) || !file_exists($file_path)) {
            return false;
        }

        // Check file size
        $file_size = filesize($file_path);
        if ($file_size > self::MAX_FILE_SIZE) {
            $this->log("File too large for conversion: {$file_path} ({$file_size} bytes)", 'warning');
            return false;
        }

        // Get file extension
        $extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

        // Check if extension is supported
        if (!in_array($extension, self::SUPPORTED_FORMATS)) {
            return false;
        }

        // Verify actual image type matches extension
        $image_info = @getimagesize($file_path);
        if (!$image_info) {
            $this->log("Invalid image file: {$file_path}", 'warning');
            return false;
        }

        $mime_type = $image_info['mime'];
        $valid_types = array(
            'image/jpeg',
            'image/png',
            'image/gif'
        );

        return in_array($mime_type, $valid_types);
    }



    /**
     * Enhanced WebP conversion with comprehensive error handling
     */
    public function convert_to_webp($source_path, $custom_settings = null) {
        $this->log("Starting WebP conversion for: {$source_path}", 'info');

        // Validate input
        if (!$this->is_convertible_image($source_path)) {
            throw new Exception("File is not convertible: {$source_path}");
        }

        if (!$this->can_convert_webp()) {
            throw new Exception("WebP conversion not supported on this server");
        }

        // Use custom settings or default
        $settings = $custom_settings ?: $this->settings;

        // Generate WebP path
        $webp_path = $this->get_webp_path($source_path);
        if (!$webp_path) {
            throw new Exception("Could not generate WebP path for: {$source_path}");
        }

        // Check if WebP already exists and is newer
        if (file_exists($webp_path) && filemtime($webp_path) >= filemtime($source_path)) {
            $this->log("WebP already exists and is current: {$webp_path}", 'info');
            return $webp_path;
        }

        // Memory management
        $this->optimize_memory_for_conversion($source_path);

        try {
            // Load source image
            $source_image = $this->load_image($source_path);
            if (!$source_image) {
                throw new Exception("Failed to load source image: {$source_path}");
            }

            // Apply smart quality optimization
            $quality = $this->calculate_smart_quality($source_path, $settings);

            // Resize if needed
            $source_image = $this->resize_if_needed($source_image, $settings);

            // Convert to WebP
            $success = false;
            if ($settings['lossless']) {
                $success = imagewebp($source_image, $webp_path, 100);
            } else {
                $success = imagewebp($source_image, $webp_path, $quality);
            }

            // Clean up memory
            imagedestroy($source_image);

            if (!$success) {
                throw new Exception("imagewebp() function failed");
            }

            if (!file_exists($webp_path)) {
                throw new Exception("WebP file was not created");
            }

            // Verify WebP file integrity
            if (!$this->verify_webp_integrity($webp_path)) {
                @unlink($webp_path);
                throw new Exception("Created WebP file is corrupted");
            }

            // Log success
            $original_size = filesize($source_path);
            $webp_size = filesize($webp_path);
            $savings = $original_size - $webp_size;
            $savings_percent = $original_size > 0 ? round(($savings / $original_size) * 100, 1) : 0;

            $this->log("WebP conversion successful: {$webp_path}", 'info');
            $this->log("Size reduction: {$original_size} → {$webp_size} bytes ({$savings_percent}% savings)", 'info');

            // Update statistics
            $this->update_conversion_stats($original_size, $webp_size);

            return $webp_path;

        } catch (Exception $e) {
            $this->log("WebP conversion failed: " . $e->getMessage(), 'error');

            // Clean up any partial files
            if (file_exists($webp_path)) {
                @unlink($webp_path);
            }

            throw $e;
        }
    }

    /**
     * Load image from file with enhanced error handling
     */
    private function load_image($file_path) {
        $image_info = getimagesize($file_path);
        if (!$image_info) {
            return false;
        }

        $mime_type = $image_info['mime'];

        switch ($mime_type) {
            case 'image/jpeg':
                return @imagecreatefromjpeg($file_path);
            case 'image/png':
                return @imagecreatefrompng($file_path);
            case 'image/gif':
                return @imagecreatefromgif($file_path);
            default:
                return false;
        }
    }

    /**
     * Calculate smart quality based on image characteristics
     */
    private function calculate_smart_quality($file_path, $settings) {
        if (!$settings['smart_quality']) {
            return $settings['quality'];
        }

        $image_info = getimagesize($file_path);
        $file_size = filesize($file_path);

        // Base quality
        $quality = $settings['quality'];

        // Adjust based on file size
        if ($file_size > 2 * 1024 * 1024) { // > 2MB
            $quality -= 5;
        } elseif ($file_size < 100 * 1024) { // < 100KB
            $quality += 5;
        }

        // Adjust based on dimensions
        $pixels = $image_info[0] * $image_info[1];
        if ($pixels > 2000000) { // > 2MP
            $quality -= 5;
        }

        // Ensure quality is within bounds
        return max(30, min(100, $quality));
    }

    /**
     * Resize image if it exceeds maximum dimensions
     */
    private function resize_if_needed($image, $settings) {
        $width = imagesx($image);
        $height = imagesy($image);

        $max_width = $settings['max_width'];
        $max_height = $settings['max_height'];

        if ($width <= $max_width && $height <= $max_height) {
            return $image;
        }

        // Calculate new dimensions maintaining aspect ratio
        $ratio = min($max_width / $width, $max_height / $height);
        $new_width = round($width * $ratio);
        $new_height = round($height * $ratio);

        // Create resized image
        $resized = imagecreatetruecolor($new_width, $new_height);

        // Preserve transparency for PNG
        imagealphablending($resized, false);
        imagesavealpha($resized, true);

        // Resize
        imagecopyresampled($resized, $image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);

        // Clean up original
        imagedestroy($image);

        $this->log("Image resized from {$width}x{$height} to {$new_width}x{$new_height}", 'info');

        return $resized;
    }

    /**
     * Verify WebP file integrity
     */
    private function verify_webp_integrity($webp_path) {
        // Check if file exists and has content
        if (!file_exists($webp_path) || filesize($webp_path) < 100) {
            return false;
        }

        // Try to load the WebP file
        $webp_image = @imagecreatefromwebp($webp_path);
        if (!$webp_image) {
            return false;
        }

        imagedestroy($webp_image);
        return true;
    }

    /**
     * Optimize memory for image conversion
     */
    private function optimize_memory_for_conversion($file_path) {
        if (!$this->settings['memory_optimization']) {
            return;
        }

        // Get image dimensions to estimate memory usage
        $image_info = getimagesize($file_path);
        $width = $image_info[0];
        $height = $image_info[1];

        // Estimate memory needed (width * height * 4 bytes per pixel * 3 for processing)
        $estimated_memory = $width * $height * 4 * 3;

        // Check if we have enough memory
        $current_usage = memory_get_usage(true);
        $memory_limit = $this->get_memory_limit();
        $available = $memory_limit - $current_usage;

        if ($estimated_memory > $available * self::MEMORY_THRESHOLD) {
            // Try to increase memory limit
            $new_limit = $current_usage + $estimated_memory + (64 * 1024 * 1024); // Add 64MB buffer
            @ini_set('memory_limit', $new_limit);

            $this->log("Increased memory limit for large image conversion", 'info');
        }

        // Force garbage collection
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
    }

    /**
     * Get WebP file path from original path - ROBUST VERSION
     */
    private function get_webp_path($original_path) {

        // Validate input
        if (!$original_path || !is_string($original_path) || empty($original_path)) {
            return false;
        }

        // Use native pathinfo instead of safe wrapper for debugging
        $path_info = pathinfo($original_path);

        if (empty($path_info['dirname']) || empty($path_info['filename'])) {
            return false;
        }

        // CRITICAL FIX: Use forward slash for WordPress compatibility (WordPress normalizes to forward slashes)
        $webp_path = $path_info['dirname'] . '/' . $path_info['filename'] . '.webp';

        // Normalize path to ensure consistent forward slashes
        $webp_path = wp_normalize_path($webp_path);

        return $webp_path;
    }

    /**
     * CRITICAL: Fix broken WebP path formats from old conversions
     */
    private function fix_webp_path_format($broken_path) {
        // Common broken patterns:
        // D:xampphtdocswordpress/wp-content/uploads/2025/05filename.webp
        // Should be: D:/xampp/htdocs/wordpress/wp-content/uploads/2025/05/filename.webp

        $fixed_path = $broken_path;

        // Fix missing separators after drive letter
        $fixed_path = preg_replace('/^([A-Z]):([^\/\\\\])/', '$1:/$2', $fixed_path);

        // Fix missing separators between directories and filenames
        $fixed_path = preg_replace('/([a-z])([A-Z])/', '$1/$2', $fixed_path);
        $fixed_path = preg_replace('/(\d{2})([A-Za-z])/', '$1/$2', $fixed_path);

        // Normalize the path using WordPress function
        $fixed_path = wp_normalize_path($fixed_path);

        // If the fix didn't work, try to reconstruct from original file
        if (!file_exists($fixed_path)) {
            // Extract filename from broken path
            $filename = basename($broken_path);
            $filename_without_ext = pathinfo($filename, PATHINFO_FILENAME);

            // Try to find the original file and generate correct WebP path
            $upload_dir = wp_upload_dir();
            $year_month = date('Y/m'); // Current year/month

            // Try different possible original file extensions
            $extensions = ['jpg', 'jpeg', 'png', 'gif'];
            foreach ($extensions as $ext) {
                $possible_original = $upload_dir['basedir'] . '/' . $year_month . '/' . $filename_without_ext . '.' . $ext;
                if (file_exists($possible_original)) {
                    $reconstructed_path = $this->get_webp_path($possible_original);
                    return $reconstructed_path;
                }
            }
        }

        return file_exists($fixed_path) ? $fixed_path : false;
    }

    /**
     * Update conversion statistics
     */
    private function update_conversion_stats($original_size, $webp_size) {
        $stats = get_option('redco_webp_enhanced_stats', array(
            'total_conversions' => 0,
            'total_original_size' => 0,
            'total_webp_size' => 0,
            'total_savings' => 0,
            'last_conversion' => 0
        ));

        $stats['total_conversions']++;
        $stats['total_original_size'] += $original_size;
        $stats['total_webp_size'] += $webp_size;
        $stats['total_savings'] += ($original_size - $webp_size);
        $stats['last_conversion'] = time();

        update_option('redco_webp_enhanced_stats', $stats);
    }

    /**
     * SIMPLE FIX: Get all processable images upfront before starting conversion
     */
    public function ajax_get_processable_images() {
        if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_bulk_convert') || !current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Security check failed'));
            return;
        }

        $processable_images = $this->get_all_processable_images();

        wp_send_json_success(array(
            'total_images' => count($processable_images),
            'image_ids' => array_column($processable_images, 'ID'),
            'message' => 'Found ' . count($processable_images) . ' processable images'
        ));
    }

    /**
     * SIMPLE FIX: Get all images that can actually be processed (pre-validated)
     * CRITICAL FIX: Made public so it can be called from global AJAX handlers
     */
    public function get_all_processable_images() {
        global $wpdb;

        // Get all unconverted images from database
        $query = "
            SELECT p.ID, p.post_title, p.post_mime_type
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png')
            AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
            ORDER BY p.ID ASC
        ";

        $all_images = $wpdb->get_results($query);
        $processable_images = array();

        // Validate each image file exists and is processable
        foreach ($all_images as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            if ($file_path && file_exists($file_path) && is_readable($file_path)) {
                $processable_images[] = $image;
            }
        }

        return $processable_images;
    }

    /**
     * SIMPLE FIX: Get specific images by their IDs (no file validation needed - already done)
     */
    private function get_images_by_ids($image_ids) {
        if (empty($image_ids)) {
            return array();
        }

        $images = array();
        foreach ($image_ids as $image_id) {
            $image = get_post($image_id);
            if ($image && $image->post_type === 'attachment') {
                $images[] = $image;
            }
        }

        return $images;
    }

    /**
     * COMPATIBILITY: Regular bulk convert method for compatibility with global AJAX handler
     */
    public function ajax_bulk_convert() {
        // REMOVED: Test connection functionality

        // Simply call the enhanced version for compatibility
        $this->ajax_enhanced_bulk_convert();
    }

    /**
     * REAL Enhanced bulk conversion with actual WebP file creation
     */
    public function ajax_enhanced_bulk_convert() {
        // CRITICAL FIX: Safe performance monitoring with error handling
        try {
            if (class_exists('Redco_Performance_Monitor')) {
                Redco_Performance_Monitor::start_timer('webp_bulk_conversion');
            }
        } catch (Exception $e) {
            // Silently continue if performance monitoring fails
        }

        // FATAL ERROR FIX: Ensure WordPress media functions are loaded
        if (!function_exists('wp_get_attachment_metadata')) {
            require_once(ABSPATH . 'wp-admin/includes/image.php');
            require_once(ABSPATH . 'wp-admin/includes/file.php');
            require_once(ABSPATH . 'wp-admin/includes/media.php');
        }

        // CRITICAL FIX: Enhanced error handling with detailed logging
        try {
            // Enhanced security and validation checks

            // 1. Verify nonce for security
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_webp_bulk_convert')) {
                wp_send_json_error(array(
                    'message' => 'Security verification failed. Please refresh the page and try again.',
                    'error_code' => 'NONCE_FAILED'
                ));
                return;
            }

            // 2. Check user capabilities
            if (!current_user_can('manage_options')) {
                wp_send_json_error(array(
                    'message' => 'You do not have sufficient permissions to perform this action.',
                    'error_code' => 'INSUFFICIENT_PERMISSIONS'
                ));
                return;
            }

            // 3. Check if WebP module is enabled
            if (!redco_is_module_enabled('smart-webp-conversion')) {
                wp_send_json_error(array(
                    'message' => 'WebP conversion module is not enabled. Please enable it in the Modules page.',
                    'error_code' => 'MODULE_DISABLED'
                ));
                return;
            }
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => 'Initialization error: ' . $e->getMessage(),
                'error_code' => 'INITIALIZATION_ERROR'
            ));
            return;
        }

        // CRITICAL FIX: Enhanced parameter validation with debugging
        $batch_size = isset($_POST['batch_size']) ? intval($_POST['batch_size']) : ($this->settings['batch_size'] ?? 5);
        $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;
        $image_ids = isset($_POST['image_ids']) ? array_map('intval', $_POST['image_ids']) : array();

        // CRITICAL FIX: Debug logging for troubleshooting
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 WebP AJAX Debug - POST data: ' . print_r($_POST, true));
            error_log('🔧 WebP AJAX Debug - Batch size: ' . $batch_size);
            error_log('🔧 WebP AJAX Debug - Offset: ' . $offset);
            error_log('🔧 WebP AJAX Debug - Image IDs: ' . print_r($image_ids, true));
        }

        // Validate batch size
        if ($batch_size < 1 || $batch_size > 50) {
            wp_send_json_error(array(
                'message' => 'Invalid batch size. Must be between 1 and 50.',
                'error_code' => 'INVALID_BATCH_SIZE'
            ));
            return;
        }

        $this->log("Enhanced bulk conversion starting - Batch: {$batch_size}, Offset: {$offset}, Image IDs: " . count($image_ids), 'info');

        // 4. Check server capabilities for WebP conversion
        if (!$this->can_convert_webp()) {
            $gd_info = function_exists('gd_info') ? gd_info() : array();
            $webp_support = isset($gd_info['WebP Support']) ? $gd_info['WebP Support'] : false;

            wp_send_json_error(array(
                'message' => 'Your server does not support WebP conversion. Please contact your hosting provider to enable GD library with WebP support.',
                'error_code' => 'WEBP_NOT_SUPPORTED'
            ));
            return;
        }

        try {
            // CRITICAL FIX: Enhanced error handling for image retrieval
            if (!empty($image_ids)) {
                $images = $this->get_images_by_ids($image_ids);
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 WebP Debug - Retrieved ' . count($images) . ' images by IDs');
                }
            } else {
                // Fallback to original method for compatibility
                $images = $this->get_real_unconverted_images($batch_size, $offset);
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 WebP Debug - Retrieved ' . count($images) . ' images by offset');
                }
            }

            if (empty($images)) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 WebP Debug - No images found to convert');
                }

                wp_send_json_success(array(
                    'message' => 'No more images to convert',
                    'processed' => 0,
                    'has_more' => false,
                    'total_processed' => $offset
                ));
                return;
            }

            // CRITICAL FIX: Remove premature has_more check - determine after processing current batch

            // CRITICAL FIX: Enhanced error handling for total calculation
            try {
                $total_convertible_images = $this->get_cached_initial_total();
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 WebP Debug - Total convertible images: ' . $total_convertible_images);
                }
            } catch (Exception $e) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 WebP Debug - Error getting total: ' . $e->getMessage());
                }
                $total_convertible_images = count($images); // Fallback to current batch count
            }

            $results = array(
                'processed' => 0,
                'errors' => array(),
                'conversions' => array(),
                'has_more' => false, // Will be determined after processing
                'total_images' => $total_convertible_images, // CRITICAL FIX: Include total for accurate progress
                'current_operation' => 'Starting batch conversion...',
                'progress_messages' => array()
            );

            // Process images in batch

            foreach ($images as $image) {
                try {
                    $file_path = redco_safe_get_attached_file($image->ID);

                    if (!$file_path || !file_exists($file_path)) {
                        $error = "File not found for image ID {$image->ID}: {$file_path}";
                        $results['errors'][] = $error;
                        continue;
                    }

                    // REAL-TIME FEEDBACK: Add progress messages
                    $results['current_operation'] = "Converting image: " . basename($file_path);
                    $results['progress_messages'][] = "📸 Processing: " . $image->post_title;

                    // REAL WebP conversion with detailed error reporting
                    $webp_result = $this->real_webp_conversion($file_path, $image->ID);

                    if ($webp_result['success']) {
                        $results['conversions'][] = array(
                            'id' => $image->ID,
                            'original_file' => basename($file_path),
                            'webp_file' => basename($webp_result['webp_path']),
                            'original_size' => $webp_result['original_size'],
                            'webp_size' => $webp_result['webp_size'],
                            'savings' => $webp_result['savings']
                        );

                        $results['processed']++;

                    } else {
                        $error = "Failed to convert image ID {$image->ID}: {$webp_result['error']}";
                        $results['errors'][] = $error;
                    }

                } catch (Exception $e) {
                    $error_msg = "Exception converting image ID {$image->ID}: " . $e->getMessage();
                    $results['errors'][] = $error_msg;
                    $this->log($error_msg, 'error');
                }

                // Memory management
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }

            // SIMPLE FIX: For pre-validated lists, has_more is determined by JavaScript
            // For fallback offset-based processing, use the original logic
            if (!empty($image_ids)) {
                // Pre-validated list - JavaScript handles continuation
                $results['has_more'] = false; // JavaScript will determine if more batches needed
            } else {
                // Fallback offset-based processing
                $images_attempted = count($images);
                $next_offset = $offset + $images_attempted;
                $results['has_more'] = $this->check_has_more_images($next_offset);
            }

            // Return conversion results
            $error_count = count($results['errors']);
            $message = "Conversion completed: {$results['processed']} images processed, {$error_count} errors";

            // CRITICAL FIX: Safe performance monitoring end with error handling
            $execution_time = 0;
            try {
                if (class_exists('Redco_Performance_Monitor')) {
                    $execution_time = Redco_Performance_Monitor::end_timer('webp_bulk_conversion');
                }
            } catch (Exception $e) {
                // Silently continue if performance monitoring fails
                $execution_time = 0;
            }

            wp_send_json_success(array(
                'message' => $message,
                'processed' => $results['processed'],
                'errors' => $results['errors'],
                'conversions' => $results['conversions'],
                'has_more' => $results['has_more'],
                'total_processed' => $offset + $results['processed'],
                'total_images' => $results['total_images'],
                'execution_time' => round($execution_time, 2)
            ));

        } catch (Exception $e) {
            // CRITICAL FIX: Safe performance monitoring end on error
            try {
                if (class_exists('Redco_Performance_Monitor')) {
                    Redco_Performance_Monitor::end_timer('webp_bulk_conversion');
                }
            } catch (Exception $monitor_error) {
                // Silently continue if performance monitoring fails
            }

            $error_msg = "REAL bulk conversion error: " . $e->getMessage();
            $this->log($error_msg, 'error');
            wp_send_json_error($error_msg);
        }
    }

    /**
     * Get REAL unconverted images with proper validation
     */
    private function get_real_unconverted_images($limit = 10, $offset = 0) {
        global $wpdb;

        // Get all image attachments that haven't been converted yet
        $query = "
            SELECT p.ID, p.post_title, p.post_mime_type
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png')
            AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
            ORDER BY p.ID ASC
            LIMIT %d OFFSET %d
        ";

        $images = $wpdb->get_results($wpdb->prepare($query, $limit, $offset));

        return $images;
    }

    /**
     * Check if there are more images to process at the next offset
     */
    private function check_has_more_images($next_offset) {
        // Simply try to get one image at the next offset
        $next_batch = $this->get_real_unconverted_images(1, $next_offset);
        $has_more = !empty($next_batch);

        return $has_more;
    }

    /**
     * REAL WebP conversion method that actually creates files
     */
    private function real_webp_conversion($file_path, $attachment_id) {
        try {
            // Validate input
            if (!file_exists($file_path)) {
                return array('success' => false, 'error' => 'Source file does not exist');
            }

            if (!$this->is_convertible_image($file_path)) {
                return array('success' => false, 'error' => 'File is not a convertible image format');
            }

            // Get original file size
            $original_size = filesize($file_path);
            if ($original_size === false) {
                return array('success' => false, 'error' => 'Could not get original file size');
            }

            // Generate WebP path
            $webp_path = $this->get_webp_path($file_path);
            if (!$webp_path) {
                return array('success' => false, 'error' => 'Could not generate WebP path');
            }

            // Check if directory is writable
            $webp_dir = dirname($webp_path);
            if (!is_writable($webp_dir)) {
                return array('success' => false, 'error' => "Directory not writable: {$webp_dir}");
            }

            // Load the image
            $source_image = $this->load_image($file_path);
            if (!$source_image) {
                $error = 'Failed to load source image';
                return array('success' => false, 'error' => $error);
            }

            // SETTINGS FIX: Use all relevant settings for conversion
            $quality = $this->settings['quality'] ?? 80;
            $use_lossless = $this->settings['lossless'] ?? false;

            // FATAL ERROR FIX: Get MIME type safely without wp_get_attachment_mime_type()
            $mime_type = get_post_mime_type($attachment_id);
            if (!$mime_type) {
                // Fallback: detect from file extension
                $file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
                switch ($file_extension) {
                    case 'png':
                        $mime_type = 'image/png';
                        break;
                    case 'jpg':
                    case 'jpeg':
                        $mime_type = 'image/jpeg';
                        break;
                    case 'gif':
                        $mime_type = 'image/gif';
                        break;
                    default:
                        $mime_type = 'image/jpeg'; // Default fallback
                }
            }

            // Apply lossless setting for PNG images or when explicitly enabled
            if ($use_lossless || $mime_type === 'image/png') {
                $quality = 100; // Lossless WebP
            }

            $success = imagewebp($source_image, $webp_path, $quality);

            // Clean up memory
            imagedestroy($source_image);

            if (!$success) {
                $error = 'imagewebp() function failed';
                return array('success' => false, 'error' => $error);
            }

            // Verify the file was created
            if (!file_exists($webp_path)) {
                $error = 'WebP file was not created on disk';
                return array('success' => false, 'error' => $error);
            }

            $webp_size = filesize($webp_path);
            if ($webp_size === false) {
                return array('success' => false, 'error' => 'Could not get WebP file size');
            }

            // PERFORMANCE FIX: Make thumbnail conversion optional for faster processing
            $webp_sizes = array('full' => $webp_path);
            $total_original_size = $original_size;
            $total_webp_size = $webp_size;

            // Only convert thumbnails if specifically enabled (disabled by default for performance)
            $convert_thumbnails = $this->settings['convert_thumbnails'] ?? false;

            if ($convert_thumbnails) {
                // Get attachment metadata to find all generated sizes
                $metadata = wp_get_attachment_metadata($attachment_id);
                if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
                    $upload_dir = wp_upload_dir();
                    $file_dirname = dirname($file_path);

                    foreach ($metadata['sizes'] as $size_name => $size_data) {
                        if (!isset($size_data['file']) || empty($size_data['file'])) {
                            continue;
                        }

                        $size_file_path = path_join($file_dirname, $size_data['file']);

                        if (file_exists($size_file_path) && $this->is_convertible_image($size_file_path)) {
                            try {
                                $size_webp_path = $this->get_webp_path($size_file_path);



                                // Load and convert the size image
                                $size_image = $this->load_image($size_file_path);
                                if ($size_image) {
                                    $size_success = imagewebp($size_image, $size_webp_path, $quality);
                                    imagedestroy($size_image);

                                    if ($size_success && file_exists($size_webp_path)) {
                                        $webp_sizes[$size_name] = $size_webp_path;

                                        // Add to total sizes for statistics
                                        $size_original_size = filesize($size_file_path);
                                        $size_webp_size = filesize($size_webp_path);
                                        $total_original_size += $size_original_size;
                                        $total_webp_size += $size_webp_size;

                                        // Always keep original size files as backup
                                    }
                                }
                            } catch (Exception $e) {
                                // Size conversion failed, continue with other sizes
                            }
                        }
                    }
                }
            }

            // CRITICAL FIX: Convert file paths to URLs for Media Library compatibility
            $upload_dir = wp_upload_dir();
            $webp_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $webp_path);
            $webp_url = wp_normalize_path($webp_url);
            $webp_url = str_replace('\\', '/', $webp_url); // Ensure forward slashes for URLs

            // Convert WebP size paths to URLs
            $webp_size_urls = array();
            foreach ($webp_sizes as $size_name => $size_path) {
                $size_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $size_path);
                $size_url = wp_normalize_path($size_url);
                $size_url = str_replace('\\', '/', $size_url); // Ensure forward slashes for URLs
                $webp_size_urls[$size_name] = $size_url;
            }

            // RECENT CONVERSIONS FIX: Store conversion metadata with enhanced data
            $conversion_data = array(
                'converted' => true,
                'conversion_date' => current_time('mysql'),
                'conversion_timestamp' => time(),
                'original_size' => $total_original_size,
                'webp_size' => $total_webp_size,
                'savings' => $total_original_size - $total_webp_size,
                'savings_percentage' => $total_original_size > 0 ? round((($total_original_size - $total_webp_size) / $total_original_size) * 100, 2) : 0,
                'quality' => $quality,
                'method' => 'enhanced_bulk_with_sizes',
                'webp_path' => $webp_path,        // Keep file path for file operations
                'webp_url' => $webp_url,          // CRITICAL FIX: Add URL for Media Library
                'webp_sizes' => $webp_sizes,      // Keep file paths for file operations
                'webp_size_urls' => $webp_size_urls, // CRITICAL FIX: Add URLs for Media Library
                'sizes_converted' => count($webp_sizes),
                'file_path' => $file_path,
                'attachment_title' => get_the_title($attachment_id)
            );

            update_post_meta($attachment_id, '_webp_conversion_data', $conversion_data);

            // Clear WebP-related cache after conversion
            $this->clear_webp_cache();

            // RECENT CONVERSIONS FIX: Also store in a separate recent conversions log
            $recent_conversions = get_option('redco_webp_recent_conversions', array());

            // Add this conversion to the beginning of the array
            array_unshift($recent_conversions, array(
                'attachment_id' => $attachment_id,
                'title' => get_the_title($attachment_id),
                'conversion_date' => current_time('mysql'),
                'original_size' => $total_original_size,
                'webp_size' => $total_webp_size,
                'savings' => $total_original_size - $total_webp_size,
                'savings_percentage' => $total_original_size > 0 ? round((($total_original_size - $total_webp_size) / $total_original_size) * 100, 2) : 0
            ));

            // Keep only the last 50 conversions in storage (but display max 10 for UI)
            $recent_conversions = array_slice($recent_conversions, 0, 50);
            update_option('redco_webp_recent_conversions', $recent_conversions);

            // CRITICAL FIX: Store original metadata before any modifications
            $this->preserve_original_metadata($attachment_id);

            // DO NOT update WordPress core metadata - this breaks original file deletion
            // Instead, WebP serving is handled through hooks and conversion data

            // Always keep original files as backup for safety

            return array(
                'success' => true,
                'webp_path' => $webp_path,
                'original_size' => $total_original_size,
                'webp_size' => $total_webp_size,
                'savings' => $total_original_size - $total_webp_size,
                'original_deleted' => false, // Always keep originals as backup
                'sizes_converted' => count($webp_sizes),
                'webp_sizes' => $webp_sizes
            );

        } catch (Exception $e) {
            return array('success' => false, 'error' => 'Exception: ' . $e->getMessage());
        }
    }

    /**
     * Get unconverted images with enhanced filtering
     */
    private function get_unconverted_images($limit = 10, $offset = 0) {
        global $wpdb;

        $query = "
            SELECT p.ID, p.post_title, p.post_mime_type
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
            AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
            ORDER BY p.ID ASC
            LIMIT %d OFFSET %d
        ";

        $images = $wpdb->get_results($wpdb->prepare($query, $limit, $offset));

        // Additional filtering for file existence and convertibility
        $valid_images = array();
        foreach ($images as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            if ($file_path && $this->is_convertible_image($file_path)) {
                $valid_images[] = $image;
            }
        }

        return $valid_images;
    }

    /**
     * CRITICAL FIX: Preserve original metadata for WordPress core deletion
     * This ensures WordPress can still delete original files while WebP files are served via hooks
     */
    private function preserve_original_metadata($attachment_id) {
        // Get current attachment metadata (original file paths)
        $original_metadata = wp_get_attachment_metadata($attachment_id);
        $original_file_path = get_post_meta($attachment_id, '_wp_attached_file', true);

        if (!is_array($original_metadata)) {
            $original_metadata = array();
        }

        // Store original metadata in our custom field for restoration if needed
        $backup_data = array(
            'original_attached_file' => $original_file_path,
            'original_metadata' => $original_metadata,
            'backup_timestamp' => time()
        );

        update_post_meta($attachment_id, '_webp_original_backup', $backup_data);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔧 METADATA PRESERVATION: Backed up original metadata for attachment {$attachment_id}");
            error_log("🔧 Original file: " . $original_file_path);
            error_log("🔧 Original metadata sizes: " . (isset($original_metadata['sizes']) ? count($original_metadata['sizes']) : 0));
        }

        // CRITICAL: DO NOT modify WordPress core metadata
        // This allows WordPress to continue using original file paths for deletion
        // WebP serving is handled through our hooks using _webp_conversion_data
    }

    /**
     * DEPRECATED: Metadata fixing methods removed
     * These methods were breaking WordPress core file deletion by overwriting metadata
     * Original metadata is now preserved to allow WordPress core deletion to work properly
     */

    /**
     * Enhanced test conversion
     */
    public function ajax_enhanced_test() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            // Test server capabilities
            $capabilities = array(
                'webp_support' => $this->can_convert_webp(),
                'memory_available' => $this->check_memory_availability(),
                'write_permissions' => $this->check_write_permissions(),
                'gd_info' => function_exists('gd_info') ? gd_info() : array()
            );

            // Test with a sample image if available
            $test_result = null;
            $sample_images = $this->get_unconverted_images(1, 0);

            if (!empty($sample_images)) {
                $test_image = $sample_images[0];
                $file_path = redco_safe_get_attached_file($test_image->ID);

                if ($file_path) {
                    try {
                        $webp_path = $this->convert_to_webp($file_path);
                        $test_result = array(
                            'success' => true,
                            'image_id' => $test_image->ID,
                            'original_file' => basename($file_path),
                            'webp_file' => basename($webp_path),
                            'original_size' => filesize($file_path),
                            'webp_size' => filesize($webp_path)
                        );
                    } catch (Exception $e) {
                        $test_result = array(
                            'success' => false,
                            'error' => $e->getMessage()
                        );
                    }
                }
            }

            wp_send_json_success(array(
                'capabilities' => $capabilities,
                'test_conversion' => $test_result,
                'logs' => array_slice($this->errors, -10) // Last 10 log entries
            ));

        } catch (Exception $e) {
            wp_send_json_error('Test failed: ' . $e->getMessage());
        }
    }

    /**
     * Enhanced statistics
     */
    public function ajax_enhanced_stats() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            // REAL-TIME STATS: Use the updated get_stats method for consistent data
            $stats = $this->get_stats();

            wp_send_json_success(array(
                'total_images' => $stats['total_images'],
                'converted_images' => $stats['converted_images'],
                'unconverted_images' => $stats['unconverted_images'],
                'conversion_percentage' => $stats['conversion_percentage'],
                'total_original_size' => $stats['total_original_size'],
                'total_webp_size' => $stats['total_webp_size'],
                'total_savings' => $stats['total_savings'],
                'savings_percentage' => $stats['savings_percentage'],
                'compression_ratio' => $stats['total_original_size'] > 0 ? round((1 - ($stats['total_webp_size'] / $stats['total_original_size'])) * 100, 1) : 0,
                'formatted_savings' => size_format($stats['total_savings']),
                'formatted_original_size' => size_format($stats['total_original_size']),
                'formatted_webp_size' => size_format($stats['total_webp_size']),
                'recent_conversions' => $stats['recent_conversions'],
                'server_support' => $stats['server_support'],
                'browser_support' => $stats['browser_support']
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to get statistics: ' . $e->getMessage());
        }
    }

    /**
     * FIXED: Auto-convert new uploads AFTER complete upload including thumbnails
     */
    public function auto_convert_after_complete_upload($metadata, $attachment_id) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🚀 AUTO-CONVERT: New attachment uploaded, ID: ' . $attachment_id);
        }

        // Get current settings
        $this->settings = $this->get_settings();

        // Check if auto-convert is enabled
        if (!$this->settings['auto_convert_uploads']) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🚀 AUTO-CONVERT: Disabled - skipping conversion for attachment ' . $attachment_id);
            }
            return $metadata;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🚀 AUTO-CONVERT: Enabled - proceeding with conversion for attachment ' . $attachment_id);
        }

        // Get the file path
        $file_path = redco_safe_get_attached_file($attachment_id);
        if (!$file_path || !file_exists($file_path)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🚀 AUTO-CONVERT: File not found for attachment ' . $attachment_id . ': ' . $file_path);
            }
            return $metadata;
        }

        // Check if it's a convertible image
        if (!$this->is_convertible_image($file_path)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🚀 AUTO-CONVERT: File not convertible for attachment ' . $attachment_id . ': ' . $file_path);
            }
            return $metadata;
        }

        try {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🚀 AUTO-CONVERT: Starting conversion using bulk method for attachment ' . $attachment_id);
            }

            // Use the WORKING bulk conversion method!
            $conversion_result = $this->real_webp_conversion($file_path, $attachment_id);

            if ($conversion_result['success']) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🚀 AUTO-CONVERT: ✅ SUCCESS! Converted attachment ' . $attachment_id . ' with ' . $conversion_result['sizes_converted'] . ' sizes');
                }
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🚀 AUTO-CONVERT: ❌ FAILED! Error for attachment ' . $attachment_id . ': ' . $conversion_result['error']);
                }
            }

        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🚀 AUTO-CONVERT: ❌ EXCEPTION for attachment ' . $attachment_id . ': ' . $e->getMessage());
            }
        }

        // CRITICAL: Always return metadata to not break WordPress
        return $metadata;
    }

    /**
     * DEPRECATED: Old upload hook method - replaced with auto_convert_new_upload
     */
    public function handle_upload_conversion($upload, $context) {
        // CRITICAL DEBUG: Log all parameters to identify issues
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED handle_upload_conversion called');
            error_log('🔧 ENHANCED Upload parameter: ' . var_export($upload, true));
            error_log('🔧 ENHANCED Context parameter: ' . var_export($context, true));
        }

        // CRITICAL FIX: Comprehensive upload validation (same as regular class)
        if (!is_array($upload)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload is not an array: ' . gettype($upload));
            }
            return $upload;
        }

        if (!isset($upload['file'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload array missing file key');
            }
            return $upload;
        }

        if ($upload['file'] === null || $upload['file'] === '') {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload file is null or empty');
            }
            return $upload;
        }

        if (!is_string($upload['file'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload file is not a string: ' . gettype($upload['file']));
            }
            return $upload;
        }

        // Additional safety check for the file path
        $file_path = $upload['file'];
        if (strpos($file_path, "\0") !== false) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload file contains null bytes');
            }
            return $upload;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('✅ ENHANCED Upload file validation passed: ' . $file_path);
        }

        // Get current settings
        $this->settings = $this->get_settings();

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 Auto-convert setting: ' . ($this->settings['auto_convert_uploads'] ? 'ENABLED' : 'DISABLED'));
        }

        // SIMPLE LOGIC: Check if auto-convert is enabled
        if (!$this->settings['auto_convert_uploads']) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 Auto-convert uploads DISABLED - skipping conversion');
            }
            return $upload;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 Auto-convert uploads ENABLED - proceeding with conversion');
        }

        if (!$this->is_convertible_image($file_path)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 ENHANCED File is not convertible: ' . $file_path);
            }
            return $upload;
        }

        try {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 ENHANCED Starting WebP conversion for: ' . $file_path);
            }

            $webp_path = $this->convert_to_webp($file_path);
            if ($webp_path && file_exists($webp_path)) {
                // Store conversion info in metadata
                $upload['webp_converted'] = true;
                $upload['webp_path'] = $webp_path;
                $upload['original_size'] = filesize($file_path);
                $upload['webp_size'] = filesize($webp_path);

                $this->log("Upload converted to WebP: " . basename($webp_path), 'info');

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('✅ ENHANCED WebP conversion successful: ' . basename($webp_path));
                }
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('❌ ENHANCED WebP conversion failed or file not created');
                }
            }
        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload conversion exception: ' . $e->getMessage());
                error_log('❌ ENHANCED Exception trace: ' . $e->getTraceAsString());
            }
            $this->log("Upload conversion failed: " . $e->getMessage(), 'error');

            // CRITICAL: Don't let exceptions break the upload process
            // Return the original upload array so WordPress can continue
        } catch (Error $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Upload conversion fatal error: ' . $e->getMessage());
                error_log('❌ ENHANCED Fatal error trace: ' . $e->getTraceAsString());
            }
            $this->log("Upload conversion fatal error: " . $e->getMessage(), 'error');

            // CRITICAL: Don't let fatal errors break the upload process
        }

        return $upload;
    }

    /**
     * Generate WebP versions for attachment metadata
     */
    public function generate_webp_versions($metadata, $attachment_id) {
        // CRITICAL DEBUG: Log the method call
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED generate_webp_versions called for attachment: ' . $attachment_id);
            error_log('🔧 ENHANCED Metadata type: ' . gettype($metadata));
        }

        if (!is_array($metadata)) {
            $metadata = array();
        }

        $file_path = redco_safe_get_attached_file($attachment_id);
        if (!$file_path) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED No file path found for attachment: ' . $attachment_id);
            }
            return $metadata;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED File path for attachment ' . $attachment_id . ': ' . $file_path);
        }

        try {
            if ($this->is_convertible_image($file_path)) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 ENHANCED Image is convertible, starting conversion...');
                }

                // Use the enhanced conversion method that converts all sizes
                $conversion_result = $this->real_webp_conversion($file_path, $attachment_id);

                if ($conversion_result['success']) {
                    $this->log("Generated WebP versions for attachment {$attachment_id} with {$conversion_result['sizes_converted']} sizes", 'info');

                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log('✅ ENHANCED WebP versions generated successfully for attachment: ' . $attachment_id);
                    }
                } else {
                    $this->log("Failed to generate WebP versions for attachment {$attachment_id}: {$conversion_result['error']}", 'error');

                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log('❌ ENHANCED WebP version generation failed: ' . $conversion_result['error']);
                    }
                }
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 ENHANCED Image is not convertible: ' . $file_path);
                }
            }
        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Exception in generate_webp_versions: ' . $e->getMessage());
                error_log('❌ ENHANCED Exception trace: ' . $e->getTraceAsString());
            }
            $this->log("Failed to generate WebP version for attachment {$attachment_id}: " . $e->getMessage(), 'error');

            // CRITICAL: Don't let exceptions break the metadata generation
        } catch (Error $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ ENHANCED Fatal error in generate_webp_versions: ' . $e->getMessage());
                error_log('❌ ENHANCED Fatal error trace: ' . $e->getTraceAsString());
            }
            $this->log("Fatal error generating WebP version for attachment {$attachment_id}: " . $e->getMessage(), 'error');

            // CRITICAL: Don't let fatal errors break the metadata generation
        }

        return $metadata;
    }

    /**
     * ENHANCED: Serve WebP images if browser supports it
     */
    public function serve_webp_if_supported($image, $attachment_id, $size, $icon) {
        // Debug logging removed for production

        // In admin area, always serve WebP if available (for thumbnails)
        // On frontend, check browser support
        if (!is_admin() && !$this->browser_supports_webp()) {
            return $image;
        }

        if (!$image) {
            return $image;
        }

        // Check if this image has been converted using enhanced method
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

        if (!$conversion_data || !isset($conversion_data['converted']) || !$conversion_data['converted']) {
            return $image;
        }

        // Determine the size key
        $size_key = 'full';
        if (is_string($size)) {
            $size_key = $size;
        } elseif (is_array($size)) {
            $size_key = 'full'; // Custom sizes default to full
        }

        // Get WebP path for the specific size
        $webp_path = '';

        // First, try to get the specific size from webp_sizes
        if (isset($conversion_data['webp_sizes']) && isset($conversion_data['webp_sizes'][$size_key])) {
            $webp_path = $conversion_data['webp_sizes'][$size_key];
        }
        // Fallback to main WebP path for 'full' size
        elseif ($size_key === 'full' && isset($conversion_data['webp_path'])) {
            $webp_path = $conversion_data['webp_path'];
        }
        // Last resort: generate WebP path from original
        else {
            $original_path = redco_safe_get_attached_file($attachment_id);
            if ($original_path) {
                // For specific sizes, try to find the size file
                if ($size_key !== 'full') {
                    $metadata = wp_get_attachment_metadata($attachment_id);
                    if (isset($metadata['sizes'][$size_key]['file'])) {
                        $size_file = path_join(dirname($original_path), $metadata['sizes'][$size_key]['file']);
                        $webp_path = $this->get_webp_path($size_file);
                    }
                } else {
                    $webp_path = $this->get_webp_path($original_path);
                }
            }
        }

        // Check if WebP file exists and serve it
        if ($webp_path && file_exists($webp_path)) {
            // Convert file path to URL
            $upload_dir = wp_upload_dir();
            $webp_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $webp_path);

            // Normalize path separators for URL
            $webp_url = wp_normalize_path($webp_url);

            // Replace the image URL with WebP URL
            $image[0] = $webp_url;

            // Update width and height if available
            if (file_exists($webp_path)) {
                $webp_info = getimagesize($webp_path);
                if ($webp_info) {
                    $image[1] = $webp_info[0]; // width
                    $image[2] = $webp_info[1]; // height
                }
            }

            // WebP served successfully
        }

        return $image;
    }

    /**
     * ENHANCED: Replace images in content with WebP versions
     */
    public function replace_images_in_content($content) {
        if (!$this->browser_supports_webp()) {
            return $content;
        }

        $settings = $this->get_settings();
        if (!$settings['replace_in_content']) {
            return $content;
        }

        // Use regex to find and replace image URLs
        $pattern = '/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i';
        return preg_replace_callback($pattern, array($this, 'replace_image_callback'), $content);
    }

    /**
     * ENHANCED: Callback for image replacement in content
     */
    private function replace_image_callback($matches) {
        $img_tag = $matches[0];
        $img_url = $matches[1];

        if (!$img_url || !is_string($img_url) || empty($img_url)) {
            return $img_tag;
        }

        // Check if this is a local image
        $upload_dir = wp_upload_dir();
        $base_url = $upload_dir['baseurl'] ?? '';

        if (!$base_url || strpos($img_url, $base_url) === false) {
            return $img_tag;
        }

        // Find corresponding WebP version
        $webp_url = $this->get_webp_url_from_original($img_url);
        if ($webp_url && $webp_url !== $img_url) {
            return str_replace($img_url, $webp_url, $img_tag);
        }

        return $img_tag;
    }

    /**
     * ENHANCED: Get WebP URL from original URL with caching
     */
    private function get_webp_url_from_original($original_url) {
        if (!$original_url || !is_string($original_url) || empty($original_url)) {
            return $original_url;
        }

        // PERFORMANCE: Use static cache for URL conversions within the same request
        static $url_cache = array();

        if (isset($url_cache[$original_url])) {
            return $url_cache[$original_url];
        }

        $upload_dir = wp_upload_dir();
        $base_url = $upload_dir['baseurl'] ?? '';
        $base_dir = $upload_dir['basedir'] ?? '';

        if (!$base_url || !$base_dir) {
            $url_cache[$original_url] = $original_url;
            return $original_url;
        }

        // Convert URL to file path
        $file_path = str_replace($base_url, $base_dir, $original_url);

        // Get WebP path
        $webp_path = $this->get_webp_path($file_path);

        // Check if WebP file exists
        if ($webp_path && file_exists($webp_path)) {
            // Convert back to URL
            $webp_url = str_replace($base_dir, $base_url, $webp_path);
            $url_cache[$original_url] = $webp_url;
            return $webp_url;
        }

        $url_cache[$original_url] = $original_url;
        return $original_url;
    }

    /**
     * ENHANCED: Check if browser supports WebP
     */
    private function browser_supports_webp() {
        if (!isset($_SERVER['HTTP_ACCEPT'])) {
            return false;
        }

        $http_accept = $_SERVER['HTTP_ACCEPT'];
        if (!$http_accept || !is_string($http_accept)) {
            return false;
        }

        return strpos($http_accept, 'image/webp') !== false;
    }

    /**
     * COMPREHENSIVE: Delete both original and WebP files when attachment is deleted
     * This method runs AFTER WordPress core deletion to clean up WebP files
     * WordPress core handles original file deletion using preserved metadata
     */
    public function delete_webp_versions($attachment_id) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔥 DELETE_WEBP_VERSIONS called for attachment {$attachment_id}");
        }

        // Get conversion data for WebP files
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

        // Get original backup data to verify original files were handled
        $backup_data = get_post_meta($attachment_id, '_webp_original_backup', true);

        $deleted_files = array();
        $failed_deletions = array();
        $original_files_status = array();

        // 1. VERIFY ORIGINAL FILES DELETION (WordPress core should handle this)
        if ($backup_data && is_array($backup_data)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🔥 Checking original files deletion status...");
            }

            // Check main original file
            if (isset($backup_data['original_attached_file'])) {
                $upload_dir = wp_upload_dir();
                $original_full_path = $upload_dir['basedir'] . '/' . $backup_data['original_attached_file'];

                if (file_exists($original_full_path)) {
                    // Original file still exists - WordPress core deletion may have failed
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🔥 ⚠️ Original file still exists: " . basename($original_full_path));
                        error_log("🔥 ⚠️ WordPress core deletion may have failed - this could indicate metadata corruption");
                    }
                    $original_files_status['main_file_exists'] = true;
                } else {
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🔥 ✅ Original file properly deleted by WordPress core: " . basename($original_full_path));
                    }
                    $original_files_status['main_file_deleted'] = true;
                }
            }

            // Check original thumbnail files
            if (isset($backup_data['original_metadata']['sizes']) && is_array($backup_data['original_metadata']['sizes'])) {
                $upload_dir = wp_upload_dir();
                $original_dir = dirname($upload_dir['basedir'] . '/' . $backup_data['original_attached_file']);

                foreach ($backup_data['original_metadata']['sizes'] as $size_name => $size_data) {
                    if (isset($size_data['file'])) {
                        $original_thumb_path = $original_dir . '/' . $size_data['file'];

                        if (file_exists($original_thumb_path)) {
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log("🔥 ⚠️ Original thumbnail still exists ({$size_name}): " . basename($original_thumb_path));
                            }
                            $original_files_status['thumbnails_exist'][] = $size_name;
                        } else {
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log("🔥 ✅ Original thumbnail deleted by WordPress core ({$size_name}): " . basename($original_thumb_path));
                            }
                            $original_files_status['thumbnails_deleted'][] = $size_name;
                        }
                    }
                }
            }
        }

        // 2. DELETE WEBP FILES (our responsibility)
        if (!$conversion_data) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🔥 No WebP conversion data found for attachment {$attachment_id}");
            }
        } else {
            // Delete main WebP file
            if (isset($conversion_data['webp_path']) && $conversion_data['webp_path']) {
                $webp_path = $conversion_data['webp_path'];

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("🔥 Attempting to delete main WebP file: {$webp_path}");
                }

                if (file_exists($webp_path)) {
                    if (@unlink($webp_path)) {
                        $deleted_files[] = $webp_path;
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("🔥 ✅ Successfully deleted main WebP file: " . basename($webp_path));
                        }
                    } else {
                        $failed_deletions[] = $webp_path;
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("🔥 ❌ Failed to delete main WebP file: " . basename($webp_path));
                        }
                    }
                } else {
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🔥 ⚠️ Main WebP file not found: " . basename($webp_path));
                    }
                }
            }

            // Delete all WebP thumbnail variants
            if (isset($conversion_data['webp_sizes']) && is_array($conversion_data['webp_sizes'])) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("🔥 Found " . count($conversion_data['webp_sizes']) . " WebP thumbnail variants to delete");
                }

                foreach ($conversion_data['webp_sizes'] as $size_name => $webp_size_path) {
                    if ($webp_size_path && file_exists($webp_size_path)) {
                        if (@unlink($webp_size_path)) {
                            $deleted_files[] = $webp_size_path;
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log("🔥 ✅ Successfully deleted WebP thumbnail ({$size_name}): " . basename($webp_size_path));
                            }
                        } else {
                            $failed_deletions[] = $webp_size_path;
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log("🔥 ❌ Failed to delete WebP thumbnail ({$size_name}): " . basename($webp_size_path));
                            }
                        }
                    } else {
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("🔥 ⚠️ WebP thumbnail not found ({$size_name}): " . basename($webp_size_path));
                        }
                    }
                }
            }
        }

        // 3. Clean up all WebP-related metadata
        delete_post_meta($attachment_id, '_webp_conversion_data');
        delete_post_meta($attachment_id, '_webp_original_backup');

        // 4. Remove from recent conversions log
        $this->remove_from_recent_conversions($attachment_id);

        // 5. Clear related cache
        $this->clear_webp_cache();

        // 6. Comprehensive logging summary
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔥 COMPREHENSIVE DELETION SUMMARY for attachment {$attachment_id}:");
            error_log("🔥   === ORIGINAL FILES (WordPress Core) ===");
            if (isset($original_files_status['main_file_deleted'])) {
                error_log("🔥   ✅ Main original file: DELETED");
            } elseif (isset($original_files_status['main_file_exists'])) {
                error_log("🔥   ❌ Main original file: STILL EXISTS (WordPress core deletion failed)");
            } else {
                error_log("🔥   ⚠️ Main original file: STATUS UNKNOWN");
            }

            if (isset($original_files_status['thumbnails_deleted'])) {
                error_log("🔥   ✅ Original thumbnails deleted: " . count($original_files_status['thumbnails_deleted']));
            }
            if (isset($original_files_status['thumbnails_exist'])) {
                error_log("🔥   ❌ Original thumbnails still exist: " . count($original_files_status['thumbnails_exist']));
            }

            error_log("🔥   === WEBP FILES (Our Responsibility) ===");
            error_log("🔥   ✅ WebP files deleted: " . count($deleted_files));
            error_log("🔥   ❌ WebP deletion failures: " . count($failed_deletions));
            error_log("🔥   ✅ Metadata cleaned up: YES");
            error_log("🔥   ✅ Recent conversions cleaned up: YES");

            if (!empty($failed_deletions)) {
                error_log("🔥 ⚠️ FAILED WEBP DELETIONS:");
                foreach ($failed_deletions as $failed_file) {
                    error_log("🔥     - " . $failed_file);
                }
            }
        }

        // Return comprehensive summary
        return array(
            'webp_deleted_files' => $deleted_files,
            'webp_failed_deletions' => $failed_deletions,
            'webp_total_deleted' => count($deleted_files),
            'webp_total_failed' => count($failed_deletions),
            'original_files_status' => $original_files_status
        );
    }

    /**
     * FALLBACK: Force delete original files if WordPress core deletion failed
     * This method should only be called if original files still exist after WordPress deletion
     */
    private function force_delete_original_files($attachment_id, $backup_data) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔥 FALLBACK: Force deleting original files for attachment {$attachment_id}");
        }

        $deleted_originals = array();
        $failed_original_deletions = array();

        if (!is_array($backup_data)) {
            return array('deleted' => $deleted_originals, 'failed' => $failed_original_deletions);
        }

        $upload_dir = wp_upload_dir();

        // Force delete main original file
        if (isset($backup_data['original_attached_file'])) {
            $original_full_path = $upload_dir['basedir'] . '/' . $backup_data['original_attached_file'];

            if (file_exists($original_full_path)) {
                if (@unlink($original_full_path)) {
                    $deleted_originals[] = $original_full_path;
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🔥 ✅ FALLBACK: Force deleted main original file: " . basename($original_full_path));
                    }
                } else {
                    $failed_original_deletions[] = $original_full_path;
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🔥 ❌ FALLBACK: Failed to force delete main original file: " . basename($original_full_path));
                    }
                }
            }
        }

        // Force delete original thumbnail files
        if (isset($backup_data['original_metadata']['sizes']) && is_array($backup_data['original_metadata']['sizes'])) {
            $original_dir = dirname($upload_dir['basedir'] . '/' . $backup_data['original_attached_file']);

            foreach ($backup_data['original_metadata']['sizes'] as $size_name => $size_data) {
                if (isset($size_data['file'])) {
                    $original_thumb_path = $original_dir . '/' . $size_data['file'];

                    if (file_exists($original_thumb_path)) {
                        if (@unlink($original_thumb_path)) {
                            $deleted_originals[] = $original_thumb_path;
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log("🔥 ✅ FALLBACK: Force deleted original thumbnail ({$size_name}): " . basename($original_thumb_path));
                            }
                        } else {
                            $failed_original_deletions[] = $original_thumb_path;
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log("🔥 ❌ FALLBACK: Failed to force delete original thumbnail ({$size_name}): " . basename($original_thumb_path));
                            }
                        }
                    }
                }
            }
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔥 FALLBACK SUMMARY: Deleted " . count($deleted_originals) . " original files, " . count($failed_original_deletions) . " failures");
        }

        return array('deleted' => $deleted_originals, 'failed' => $failed_original_deletions);
    }

    /**
     * Remove attachment from recent conversions log
     */
    private function remove_from_recent_conversions($attachment_id) {
        $recent_conversions = get_option('redco_webp_recent_conversions', array());

        if (empty($recent_conversions)) {
            return;
        }

        // Filter out the deleted attachment
        $updated_conversions = array_filter($recent_conversions, function($conversion) use ($attachment_id) {
            return isset($conversion['attachment_id']) && $conversion['attachment_id'] != $attachment_id;
        });

        // Update the option if changes were made
        if (count($updated_conversions) !== count($recent_conversions)) {
            update_option('redco_webp_recent_conversions', array_values($updated_conversions));

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🔥 Removed attachment {$attachment_id} from recent conversions log");
            }
        }
    }

    /**
     * COMPREHENSIVE: Cleanup orphaned WebP files
     * Finds and removes WebP files that no longer have corresponding original images
     */
    public function cleanup_orphaned_webp_files() {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔥 CLEANUP_ORPHANED_WEBP_FILES called");
        }

        $upload_dir = wp_upload_dir();
        $upload_path = $upload_dir['basedir'];

        $orphaned_files = array();
        $cleaned_files = array();
        $failed_cleanups = array();

        try {
            // Find all WebP files in upload directory
            $webp_files = $this->find_webp_files_recursive($upload_path);

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🔥 Found " . count($webp_files) . " WebP files to check");
            }

            foreach ($webp_files as $webp_file) {
                // Check if corresponding original image exists
                $original_file = $this->get_original_from_webp_path($webp_file);

                if (!$original_file || !file_exists($original_file)) {
                    // Check if this WebP file is referenced in database
                    if (!$this->is_webp_file_referenced($webp_file)) {
                        $orphaned_files[] = $webp_file;

                        // Attempt to delete orphaned file
                        if (@unlink($webp_file)) {
                            $cleaned_files[] = $webp_file;
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log("🔥 ✅ Cleaned orphaned WebP file: " . basename($webp_file));
                            }
                        } else {
                            $failed_cleanups[] = $webp_file;
                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log("🔥 ❌ Failed to clean orphaned WebP file: " . basename($webp_file));
                            }
                        }
                    }
                }
            }

            // Log cleanup summary
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🔥 ORPHANED CLEANUP SUMMARY:");
                error_log("🔥   - Total WebP files checked: " . count($webp_files));
                error_log("🔥   - Orphaned files found: " . count($orphaned_files));
                error_log("🔥   - Files successfully cleaned: " . count($cleaned_files));
                error_log("🔥   - Failed cleanups: " . count($failed_cleanups));
            }

        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🔥 ❌ Orphaned cleanup failed: " . $e->getMessage());
            }
        }

        return array(
            'total_checked' => count($webp_files ?? array()),
            'orphaned_found' => count($orphaned_files),
            'cleaned_successfully' => count($cleaned_files),
            'failed_cleanups' => count($failed_cleanups)
        );
    }

    /**
     * Recursively find all WebP files in upload directory
     */
    private function find_webp_files_recursive($directory) {
        $webp_files = array();

        if (!is_dir($directory)) {
            return $webp_files;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::LEAVES_ONLY
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && strtolower($file->getExtension()) === 'webp') {
                $webp_files[] = $file->getPathname();
            }
        }

        return $webp_files;
    }

    /**
     * Get original image path from WebP path
     */
    private function get_original_from_webp_path($webp_path) {
        $path_info = pathinfo($webp_path);
        $directory = $path_info['dirname'];
        $filename = $path_info['filename'];

        // Try common original formats
        $possible_extensions = array('jpg', 'jpeg', 'png', 'gif');

        foreach ($possible_extensions as $ext) {
            $original_path = $directory . '/' . $filename . '.' . $ext;
            if (file_exists($original_path)) {
                return $original_path;
            }
        }

        return false;
    }

    /**
     * Check if WebP file is referenced in database
     */
    private function is_webp_file_referenced($webp_path) {
        global $wpdb;

        // Check if this WebP path is stored in conversion data
        $result = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*)
            FROM {$wpdb->postmeta}
            WHERE meta_key = '_webp_conversion_data'
            AND meta_value LIKE %s
        ", '%' . basename($webp_path) . '%'));

        return $result > 0;
    }

    /**
     * CRITICAL FIX: Serve WebP URLs in Media Library for proper thumbnail display
     * This method handles wp_get_attachment_url and wp_get_attachment_thumb_url filters
     */
    public function serve_webp_url_in_media_library($url, $attachment_id) {
        // DEBUGGING: Always log when this method is called
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🖼️ MEDIA_LIBRARY_DEBUG: serve_webp_url_in_media_library called with URL: " . $url . ", Attachment ID: " . $attachment_id);
        }

        // CRITICAL FIX: Check if URL is actually a local file path (WordPress core bug)
        $is_local_path = (strpos($url, 'D:') === 0 || strpos($url, 'C:') === 0 ||
                         (strpos($url, '/') === 0 && strpos($url, 'http') !== 0));

        if ($is_local_path) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🚨 CRITICAL: WordPress is serving local file path instead of URL: " . $url);
            }

            // Convert local path to proper URL
            $upload_dir = wp_upload_dir();
            $proper_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $url);
            $proper_url = wp_normalize_path($proper_url);
            $proper_url = str_replace('\\', '/', $proper_url);

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🔧 FIXED: Converted local path to URL: " . $proper_url);
            }

            $url = $proper_url; // Use the fixed URL for further processing
        }

        // Apply to both admin and frontend for comprehensive coverage
        // Removed admin-only restriction to fix Media Library issues

        // Skip if no URL or attachment ID
        if (!$url || !$attachment_id) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🖼️ MEDIA_LIBRARY_DEBUG: Missing URL or attachment ID, returning original");
            }
            return $url;
        }

        // Check if this image has been converted to WebP
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🖼️ MEDIA_LIBRARY_DEBUG: Conversion data for {$attachment_id}: " . print_r($conversion_data, true));
        }

        if (!$conversion_data || !isset($conversion_data['converted']) || !$conversion_data['converted']) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🖼️ MEDIA_LIBRARY_DEBUG: Image not converted, returning original URL");
            }
            return $url;
        }

        // CRITICAL FIX: Use stored WebP URL directly instead of converting file paths
        if (isset($conversion_data['webp_url']) && $conversion_data['webp_url']) {
            $webp_url = $conversion_data['webp_url'];

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🖼️ MEDIA_LIBRARY_DEBUG: ✅ Using stored WebP URL for attachment {$attachment_id}: " . $webp_url);
                error_log("🖼️ MEDIA_LIBRARY_DEBUG: Original URL: " . $url);
            }

            return $webp_url;
        }

        // FALLBACK: Handle old conversion data that only has file paths
        if (isset($conversion_data['webp_path']) && $conversion_data['webp_path']) {
            $webp_path = $conversion_data['webp_path'];

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🖼️ MEDIA_LIBRARY_DEBUG: Fallback - converting stored WebP path to URL: " . $webp_path);
            }

            // CRITICAL FIX: Normalize and fix path formatting issues
            $webp_path = wp_normalize_path($webp_path);

            // ADDITIONAL FIX: Try to fix broken path formats from database
            if (!file_exists($webp_path)) {
                $fixed_path = $this->fix_webp_path_format($webp_path);
                if ($fixed_path && file_exists($fixed_path)) {
                    $webp_path = $fixed_path;
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🖼️ MEDIA_LIBRARY_DEBUG: Fixed broken path format: " . $webp_path);
                    }
                }
            }

            // FALLBACK FIX: If path still doesn't exist, try to regenerate from original file
            if (!file_exists($webp_path)) {
                $original_file = get_attached_file($attachment_id);
                if ($original_file && file_exists($original_file)) {
                    $regenerated_webp_path = $this->get_webp_path($original_file);
                    if ($regenerated_webp_path && file_exists($regenerated_webp_path)) {
                        $webp_path = $regenerated_webp_path;
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("🖼️ MEDIA_LIBRARY_DEBUG: Regenerated WebP path: " . $webp_path);
                        }
                    }
                }
            }

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🖼️ MEDIA_LIBRARY_DEBUG: Final WebP path: " . $webp_path);
                error_log("🖼️ MEDIA_LIBRARY_DEBUG: WebP file exists: " . (file_exists($webp_path) ? 'YES' : 'NO'));
            }

            // Check if WebP file exists
            if (file_exists($webp_path)) {
                // Convert file path to URL
                $upload_dir = wp_upload_dir();
                $webp_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $webp_path);
                $webp_url = wp_normalize_path($webp_url);

                // CRITICAL FIX: Ensure URL uses forward slashes for web compatibility
                $webp_url = str_replace('\\', '/', $webp_url);

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("🖼️ MEDIA_LIBRARY_DEBUG: ✅ Serving converted WebP URL for attachment {$attachment_id}: " . basename($webp_url));
                    error_log("🖼️ MEDIA_LIBRARY_DEBUG: Original URL: " . $url);
                    error_log("🖼️ MEDIA_LIBRARY_DEBUG: WebP URL: " . $webp_url);
                }

                return $webp_url;
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("🖼️ MEDIA_LIBRARY_DEBUG: ❌ WebP file does not exist after all fixes: " . $webp_path);
                }
            }
        } else {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🖼️ MEDIA_LIBRARY_DEBUG: No webp_url or webp_path in conversion data");
            }
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🖼️ MEDIA_LIBRARY_DEBUG: Returning original URL: " . $url);
        }

        return $url;
    }

    /**
     * CRITICAL FIX: Fix Media Library WebP URLs for wp_prepare_attachment_for_js
     * This ensures that the Media Library JavaScript receives correct WebP URLs
     */
    public function fix_media_library_webp_urls($response, $attachment, $meta) {
        // DEBUGGING: Always log when this method is called
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🖼️ JS_DEBUG: fix_media_library_webp_urls called for attachment ID: " . $attachment->ID);
        }

        // CRITICAL FIX: Check if response URL is a local file path
        if (isset($response['url'])) {
            $is_local_path = (strpos($response['url'], 'D:') === 0 || strpos($response['url'], 'C:') === 0 ||
                             (strpos($response['url'], '/') === 0 && strpos($response['url'], 'http') !== 0));

            if ($is_local_path) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("🚨 JS_CRITICAL: Response URL is local file path: " . $response['url']);
                }

                // Convert local path to proper URL
                $upload_dir = wp_upload_dir();
                $proper_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $response['url']);
                $proper_url = wp_normalize_path($proper_url);
                $proper_url = str_replace('\\', '/', $proper_url);

                $response['url'] = $proper_url;

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("🔧 JS_FIXED: Converted response URL to: " . $proper_url);
                }
            }
        }

        // Process for both admin and frontend for comprehensive coverage
        // Removed admin-only restriction to fix Media Library issues

        // Only process image attachments
        if (!isset($response['type']) || $response['type'] !== 'image') {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🖼️ JS_DEBUG: Not an image attachment, returning original response");
            }
            return $response;
        }

        // Check if this image has been converted to WebP
        $conversion_data = get_post_meta($attachment->ID, '_webp_conversion_data', true);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🖼️ JS_DEBUG: Conversion data for {$attachment->ID}: " . print_r($conversion_data, true));
        }

        if (!$conversion_data || !isset($conversion_data['converted']) || !$conversion_data['converted']) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🖼️ JS_DEBUG: Image not converted, returning original response");
            }
            return $response;
        }

        // CRITICAL FIX: Use stored WebP URL directly instead of converting file paths
        if (isset($conversion_data['webp_url']) && $conversion_data['webp_url']) {
            $webp_url = $conversion_data['webp_url'];

            // Update the main URL
            $response['url'] = $webp_url;

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🖼️ JS_DEBUG: ✅ Using stored WebP URL for attachment {$attachment->ID}: " . $webp_url);
            }
        }
        // FALLBACK: Handle old conversion data that only has file paths
        else if (isset($conversion_data['webp_path']) && $conversion_data['webp_path']) {
            $webp_path = $conversion_data['webp_path'];

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🖼️ JS_DEBUG: Fallback - converting stored WebP path to URL: " . $webp_path);
            }

            // CRITICAL FIX: Normalize and fix path formatting issues
            $webp_path = wp_normalize_path($webp_path);

            // ADDITIONAL FIX: Try to fix broken path formats from database
            if (!file_exists($webp_path)) {
                $fixed_path = $this->fix_webp_path_format($webp_path);
                if ($fixed_path && file_exists($fixed_path)) {
                    $webp_path = $fixed_path;
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("🖼️ JS_DEBUG: Fixed broken path format: " . $webp_path);
                    }
                }
            }

            // FALLBACK FIX: If path still doesn't exist, try to regenerate from original file
            if (!file_exists($webp_path)) {
                $original_file = get_attached_file($attachment->ID);
                if ($original_file && file_exists($original_file)) {
                    $regenerated_webp_path = $this->get_webp_path($original_file);
                    if ($regenerated_webp_path && file_exists($regenerated_webp_path)) {
                        $webp_path = $regenerated_webp_path;
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("🖼️ JS_DEBUG: Regenerated WebP path: " . $webp_path);
                        }
                    }
                }
            }

            if (file_exists($webp_path)) {
                $upload_dir = wp_upload_dir();
                $webp_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $webp_path);
                $webp_url = wp_normalize_path($webp_url);

                // CRITICAL FIX: Ensure URL uses forward slashes for web compatibility
                $webp_url = str_replace('\\', '/', $webp_url);

                // Update the main URL
                $response['url'] = $webp_url;

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("🖼️ JS_DEBUG: ✅ Updated JavaScript URL for attachment {$attachment->ID}: " . $webp_url);
                }
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("🖼️ JS_DEBUG: ❌ WebP file does not exist after all fixes: " . $webp_path);
                }
            }
        }

        // Update all size URLs if WebP thumbnails exist
        // CRITICAL FIX: Use stored WebP size URLs directly
        if (isset($response['sizes']) && is_array($response['sizes'])) {
            // First try to use stored URLs
            if (isset($conversion_data['webp_size_urls']) && is_array($conversion_data['webp_size_urls'])) {
                foreach ($response['sizes'] as $size_name => $size_data) {
                    if (isset($conversion_data['webp_size_urls'][$size_name])) {
                        $response['sizes'][$size_name]['url'] = $conversion_data['webp_size_urls'][$size_name];

                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log("🖼️ JS_DEBUG: Updated size {$size_name} URL to stored WebP URL");
                        }
                    }
                }
            }
            // FALLBACK: Convert file paths to URLs for old conversion data
            else if (isset($conversion_data['webp_sizes']) && is_array($conversion_data['webp_sizes'])) {
                foreach ($response['sizes'] as $size_name => $size_data) {
                    if (isset($conversion_data['webp_sizes'][$size_name])) {
                        $webp_size_path = $conversion_data['webp_sizes'][$size_name];

                        if (file_exists($webp_size_path)) {
                            $upload_dir = wp_upload_dir();
                            $webp_size_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $webp_size_path);
                            $webp_size_url = wp_normalize_path($webp_size_url);
                            $webp_size_url = str_replace('\\', '/', $webp_size_url); // Ensure forward slashes

                            $response['sizes'][$size_name]['url'] = $webp_size_url;

                            if (defined('WP_DEBUG') && WP_DEBUG) {
                                error_log("🖼️ JS_DEBUG: Converted size {$size_name} path to URL (fallback)");
                            }
                        }
                    }
                }
            }
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🖼️ MEDIA_LIBRARY_JS: Fixed WebP URLs for attachment {$attachment->ID}");
        }

        return $response;
    }

    /**
     * ADDITIONAL FIX: Fix WebP srcset for responsive images
     */
    public function fix_webp_srcset($sources, $size_array, $image_src, $image_meta, $attachment_id) {
        // Only apply in admin interface
        if (!is_admin()) {
            return $sources;
        }

        // Check if this image has been converted to WebP
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

        if (!$conversion_data || !isset($conversion_data['converted']) || !$conversion_data['converted']) {
            return $sources;
        }

        // Update each source in the srcset
        if (is_array($sources) && isset($conversion_data['webp_sizes'])) {
            foreach ($sources as $width => $source) {
                // Try to find matching WebP size
                foreach ($conversion_data['webp_sizes'] as $size_name => $webp_path) {
                    if (file_exists($webp_path)) {
                        $upload_dir = wp_upload_dir();
                        $webp_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $webp_path);
                        $webp_url = wp_normalize_path($webp_url);

                        // Check if this WebP matches the source dimensions
                        $webp_filename = basename($webp_path);
                        $source_filename = basename($source['url']);

                        // Simple filename matching (could be improved)
                        if (strpos($source_filename, $size_name) !== false ||
                            strpos($webp_filename, $width . 'x') !== false) {
                            $sources[$width]['url'] = $webp_url;
                            break;
                        }
                    }
                }
            }
        }

        return $sources;
    }

    /**
     * DEBUGGING: Verify hook registration
     */
    public function debug_hook_registration() {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $hooks_to_check = array(
                'wp_get_attachment_url',
                'wp_get_attachment_thumb_url',
                'wp_prepare_attachment_for_js',
                'wp_get_attachment_image_url'
            );

            foreach ($hooks_to_check as $hook) {
                $registered = has_filter($hook, array($this, 'serve_webp_url_in_media_library'));
                if ($registered === false && $hook !== 'wp_prepare_attachment_for_js') {
                    error_log("🖼️ HOOK_DEBUG: {$hook} is NOT registered for WebP URL filtering");
                } else {
                    error_log("🖼️ HOOK_DEBUG: {$hook} is registered (priority: {$registered})");
                }
            }

            // Check wp_prepare_attachment_for_js separately
            $js_registered = has_filter('wp_prepare_attachment_for_js', array($this, 'fix_media_library_webp_urls'));
            if ($js_registered === false) {
                error_log("🖼️ HOOK_DEBUG: wp_prepare_attachment_for_js is NOT registered for WebP JS fixing");
            } else {
                error_log("🖼️ HOOK_DEBUG: wp_prepare_attachment_for_js is registered (priority: {$js_registered})");
            }
        }
    }



    /**
     * DEPRECATED: Old admin URL serving method - replaced with serve_webp_url_in_media_library
     */
    public function serve_webp_url_in_admin($url, $attachment_id) {
        // Redirect to new method
        return $this->serve_webp_url_in_media_library($url, $attachment_id);
    }

    /**
     * ENHANCED: Serve WebP image in Media Library admin
     */
    public function serve_webp_image_in_admin($html, $attachment_id, $size, $icon, $attr) {
        // Only apply in admin interface
        if (!is_admin()) {
            return $html;
        }

        // Check if this image has been converted
        $conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);

        if (!$conversion_data || !isset($conversion_data['converted']) || !$conversion_data['converted']) {
            return $html;
        }

        // Get WebP URL
        $webp_url = $this->serve_webp_url_in_admin(wp_get_attachment_url($attachment_id), $attachment_id);

        if ($webp_url && $webp_url !== wp_get_attachment_url($attachment_id)) {
            // Replace the src attribute with WebP URL
            $html = preg_replace('/src=["\']([^"\']+)["\']/', 'src="' . esc_url($webp_url) . '"', $html);

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("🔥 ADMIN_IMAGE: Replaced image HTML for attachment {$attachment_id}");
            }
        }

        return $html;
    }

    // REMOVED: serve_webp_thumb_file method - not needed for WebP conversion

    // REMOVED: serve_webp_thumb_url method - not needed for WebP conversion

    // REMOVED: All media library debug and info methods - not needed for WebP conversion

    // REMOVED: All broken media library modification code

    // REMOVED: Massive JavaScript injection method - this was causing Media Library issues

    // Test admin notice method removed

    /**
     * TEST: wp_loaded hook to verify ANY hook from our class works
     */
    public function test_wp_loaded_hook() {
        // Silent hook test - no debug output
    }

    // REMOVED: All remaining media library debugging and AJAX interception methods

    /**
     * REAL-TIME STATS: Get WebP conversion statistics using advanced caching
     */
    public function get_stats() {
        // DEBUGGING: Temporarily disable cache to ensure fresh data
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔍 WebP Stats Debug - Bypassing cache for debugging");
            return $this->calculate_stats();
        }

        // Use advanced caching system for better performance
        return Redco_Advanced_Cache::remember('webp_stats_v2', function() {
            return $this->calculate_stats();
        }, 300, Redco_Advanced_Cache::GROUP_STATS); // Cache for 5 minutes
    }

    /**
     * Calculate statistics (separated for caching)
     */
    private function calculate_stats() {
        global $wpdb;

        // CRITICAL FIX: Ensure $wpdb is available
        if (!$wpdb) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🚨 FATAL ERROR PREVENTION: $wpdb is null in get_stats()');
            }
            return array(
                'total_images' => 0,
                'converted_images' => 0,
                'unconverted_images' => 0,
                'conversion_percentage' => 0,
                'total_original_size' => 0,
                'total_webp_size' => 0,
                'total_savings' => 0,
                'savings_percentage' => 0,
                'recent_conversions' => array(),
                'server_support' => false,
                'browser_support' => false
            );
        }

        // CRITICAL FIX: Get ALL images (both converted and unconverted) for accurate stats
        // This ensures we count all images that could potentially be converted
        $all_images_query = "
            SELECT p.ID
            FROM {$wpdb->posts} p
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png')
            ORDER BY p.ID ASC
        ";

        // Use database optimizer for better performance and caching
        $all_images = Redco_Database_Optimizer::get_cached_query(
            'webp_all_images_' . md5($all_images_query),
            function() use ($wpdb, $all_images_query) {
                return $wpdb->get_results($all_images_query);
            },
            300 // Cache for 5 minutes
        );

        // CRITICAL DEBUG: Log what we found in the database
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('📊 STATS DEBUG: Found ' . count($all_images) . ' images in database with correct MIME types');

            // Log first few images for debugging
            if (count($all_images) > 0) {
                $sample_count = min(3, count($all_images));
                for ($i = 0; $i < $sample_count; $i++) {
                    $sample_image = $all_images[$i];
                    $sample_file_path = redco_safe_get_attached_file($sample_image->ID);
                    $sample_exists = $sample_file_path && file_exists($sample_file_path);
                    error_log("📊 STATS DEBUG: Sample image ID {$sample_image->ID}: file_path={$sample_file_path}, exists={$sample_exists}");
                }
            }
        }

        // REAL-TIME VALIDATION: Count only images with valid, existing files
        $total_valid_images = 0;
        $converted_images = 0;
        $total_original_size = 0;
        $total_webp_size = 0;
        $recent_conversions = array();

        foreach ($all_images as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);

            // Only count images with valid, existing files (same as conversion logic)
            if (!$file_path || !file_exists($file_path) || !is_readable($file_path)) {
                // CRITICAL DEBUG: Log why images are being skipped
                if (defined('WP_DEBUG') && WP_DEBUG && $total_valid_images < 5) {
                    error_log("📊 STATS DEBUG: Skipping image ID {$image->ID}: file_path=" . var_export($file_path, true) .
                             ", exists=" . ($file_path ? (file_exists($file_path) ? 'true' : 'false') : 'N/A') .
                             ", readable=" . ($file_path && file_exists($file_path) ? (is_readable($file_path) ? 'true' : 'false') : 'N/A'));
                }
                continue;
            }

            $total_valid_images++;

            // CRITICAL DEBUG: Log valid images found
            if (defined('WP_DEBUG') && WP_DEBUG && $total_valid_images <= 3) {
                error_log("📊 STATS DEBUG: Valid image #{$total_valid_images} - ID {$image->ID}: {$file_path}");
            }

            // Check conversion status using same logic as bulk conversion
            $conversion_data = get_post_meta($image->ID, '_webp_conversion_data', true);
            $is_converted = false;

            if (is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted'] === true) {
                // REAL-TIME VALIDATION: Verify WebP file actually exists
                $webp_path = $this->get_webp_path($file_path);
                if ($webp_path && file_exists($webp_path)) {
                    $is_converted = true;
                    $converted_images++;

                    // Calculate real file sizes for accurate savings
                    $original_size = filesize($file_path);
                    $webp_size = filesize($webp_path);

                    if ($original_size && $webp_size) {
                        $total_original_size += $original_size;
                        $total_webp_size += $webp_size;
                    }
                } else {
                    // WebP file missing - mark as not converted
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log("📊 STATS: WebP file missing for image ID {$image->ID}, marking as unconverted");
                    }
                }
            }
        }

        // Calculate final statistics using same formulas as conversion process
        $total_savings = $total_original_size - $total_webp_size;

        // CRITICAL FIX: Calculate AVG Saving % based on ALL VALID images, not just converted ones
        // Calculate total original size of all valid images (not from cache, but from the validated loop)
        $all_images_original_size = 0;
        foreach ($all_images as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            if ($file_path && file_exists($file_path) && is_readable($file_path)) {
                $original_size = filesize($file_path);
                if ($original_size) {
                    $all_images_original_size += $original_size;
                }
            }
        }

        // AVG Saving % = Total Savings / Total Original Size of ALL Images
        $savings_percentage = $all_images_original_size > 0 ? round(($total_savings / $all_images_original_size) * 100, 1) : 0;
        $unconverted_images = $total_valid_images - $converted_images;

        // CRITICAL DEBUG: Log savings percentage calculation details
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('📊 SAVINGS % CALCULATION:');
            error_log('  - Total savings: ' . size_format($total_savings));
            error_log('  - All images original size: ' . size_format($all_images_original_size));
            error_log('  - Calculated savings %: ' . $savings_percentage . '%');
            error_log('  - Formula: ' . $total_savings . ' / ' . $all_images_original_size . ' * 100 = ' . $savings_percentage);
        }

        // Get recent conversions for display (limit to 10 maximum)
        $recent_conversions = $this->get_recent_conversions(10, 0);

        $stats = array(
            'total_images' => $unconverted_images, // FIXED: Use remaining unconverted images for consistency with AJAX handler
            'converted_images' => $converted_images,
            'unconverted_images' => $unconverted_images,
            'conversion_percentage' => $total_valid_images > 0 ? round(($converted_images / $total_valid_images) * 100, 1) : 0,
            'total_original_size' => $all_images_original_size, // CRITICAL FIX: Use total size of ALL valid images
            'total_webp_size' => $total_webp_size,
            'total_savings' => $total_savings,
            'savings_percentage' => $savings_percentage,
            'recent_conversions' => $recent_conversions,
            'server_support' => $this->can_convert_webp(),
            'browser_support' => $this->browser_supports_webp()
        );

        // DEBUG: Log the class method stats for comparison with AJAX handler
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("🔍 WebP Class Stats Debug - total_images (remaining): " . $unconverted_images);
            error_log("🔍 WebP Class Stats Debug - converted_images: " . $converted_images);
        }

        // REAL-TIME DEBUG: Log statistics calculation details
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('📊 REAL-TIME STATS SUMMARY:');
            error_log('  - Database images found: ' . count($all_images));
            error_log('  - Total valid images: ' . $total_valid_images);
            error_log('  - Converted images: ' . $converted_images);
            error_log('  - Unconverted images: ' . $unconverted_images);
            error_log('  - Total savings: ' . size_format($total_savings));
            error_log('  - All images original size: ' . size_format($all_images_original_size));
            error_log('  - AVG Savings percentage (across ALL images): ' . $savings_percentage . '%');

            if ($total_valid_images === 0) {
                error_log('⚠️ STATS WARNING: No valid images found! This explains why savings_percentage is 0.');
                error_log('⚠️ STATS WARNING: Check if there are images in the media library with MIME types: image/jpeg, image/jpg, image/png');
            }
        }

        return $stats;
    }

    /**
     * Clear WebP-related cache after conversions
     */
    private function clear_webp_cache() {
        // Clear advanced cache for WebP stats
        Redco_Advanced_Cache::delete('webp_stats_v2', Redco_Advanced_Cache::GROUP_STATS);

        // Clear database optimizer cache for WebP queries
        Redco_Database_Optimizer::clear_query_cache();

        // Clear any transients related to WebP
        delete_transient('redco_webp_stats_v2');
        delete_transient('redco_webp_convertible_count');

        Redco_Error_Handler::info(
            "Cleared WebP-related cache after conversion",
            Redco_Error_Handler::CONTEXT_WEBP
        );
    }

    /**
     * PERFORMANCE FIX: Get cached total original size of all images for AVG Saving % calculation
     */
    private function get_cached_total_original_size($all_images) {
        static $cached_total_size = null;
        static $cached_image_count = null;

        // Check if we need to recalculate (if image count changed)
        $current_image_count = count($all_images);
        if ($cached_total_size === null || $cached_image_count !== $current_image_count) {
            $total_size = 0;

            // CRITICAL FIX: Add safety check for empty images array
            if (empty($all_images)) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('📊 PERFORMANCE: No images provided, returning 0 total size');
                }
                $cached_total_size = 0;
                $cached_image_count = 0;
                return 0;
            }

            // Calculate total original size of all valid images
            foreach ($all_images as $image) {
                $file_path = redco_safe_get_attached_file($image->ID);
                if ($file_path && file_exists($file_path)) {
                    $original_size = filesize($file_path);
                    if ($original_size) {
                        $total_size += $original_size;
                    }
                }
            }

            $cached_total_size = $total_size;
            $cached_image_count = $current_image_count;

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('📊 PERFORMANCE: Calculated total original size: ' . size_format($total_size) . ' for ' . $current_image_count . ' images');
            }
        }

        return $cached_total_size;
    }

    /**
     * Get count of convertible images for dynamic button state
     */
    public function get_convertible_images_count() {
        global $wpdb;

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED WEBP: get_convertible_images_count() called');
        }

        // Get all image attachments that haven't been converted yet
        $query = "
            SELECT COUNT(DISTINCT p.ID) as count
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png')
            AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
        ";

        $result = $wpdb->get_var($query);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED WEBP: Database query returned: ' . $result . ' potential images');
        }

        // Additional validation - check if files actually exist
        if ($result > 0) {
            $images_query = "
                SELECT p.ID
                FROM {$wpdb->posts} p
                LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
                WHERE p.post_type = 'attachment'
                AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png')
                AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
                LIMIT 50
            ";

            $images = $wpdb->get_results($images_query);
            $valid_count = 0;

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 ENHANCED WEBP: Checking ' . count($images) . ' images for file existence');
            }

            foreach ($images as $image) {
                $file_path = redco_safe_get_attached_file($image->ID);
                $file_exists = $file_path && file_exists($file_path);
                $is_convertible = $file_exists && $this->is_convertible_image($file_path);

                if (defined('WP_DEBUG') && WP_DEBUG && count($images) <= 5) {
                    // Only log details for first 5 images to avoid spam
                    error_log("🔧 ENHANCED WEBP: Image ID {$image->ID}: file_path={$file_path}, exists={$file_exists}, convertible={$is_convertible}");
                }

                if ($is_convertible) {
                    $valid_count++;
                }
            }

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 ENHANCED WEBP: Found ' . $valid_count . ' valid convertible images out of ' . count($images) . ' checked');
            }

            // If we checked 50 and found valid ones, estimate the total
            if (count($images) == 50 && $valid_count > 0) {
                $ratio = $valid_count / 50;
                $estimated_total = intval($result * $ratio);

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 ENHANCED WEBP: Estimated total: ' . $estimated_total . ' (ratio: ' . $ratio . ')');
                }

                return $estimated_total;
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('🔧 ENHANCED WEBP: Returning actual count: ' . $valid_count);
                }
                return $valid_count;
            }
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED WEBP: No images found in database - returning 0');
        }

        return 0;
    }

    /**
     * BATCH CALCULATION FIX: Get cached initial total that doesn't change during conversion
     * REAL-TIME STATS: Added reset parameter for statistics refresh
     */
    private function get_cached_initial_total($reset = false) {
        static $cached_total = null;

        // REAL-TIME STATS: Reset cache if requested
        if ($reset) {
            $cached_total = null;
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 ENHANCED BATCH CALCULATION: Cache reset requested');
            }
            return 0; // Return early after reset
        }

        if ($cached_total === null) {
            // Calculate the initial total once and cache it
            $cached_total = $this->calculate_initial_convertible_count();

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🔧 ENHANCED BATCH CALCULATION: Cached initial total: ' . $cached_total);
            }
        }

        return $cached_total;
    }

    /**
     * BATCH CALCULATION FIX: Calculate initial convertible count (called once at start)
     */
    private function calculate_initial_convertible_count() {
        global $wpdb;

        // CRITICAL FIX: Ensure $wpdb is available
        if (!$wpdb) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('🚨 FATAL ERROR PREVENTION: $wpdb is null in calculate_initial_convertible_count()');
            }
            return 0;
        }

        // CRITICAL FIX: Get actual convertible images by validating files exist
        // This ensures the count matches what can actually be processed
        $query = "
            SELECT p.ID
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png')
            AND (pm.meta_value IS NULL OR pm.meta_value = '' OR pm.meta_value NOT LIKE '%\"converted\":true%')
            ORDER BY p.ID ASC
        ";

        $all_unconverted = $wpdb->get_results($query);
        $valid_count = 0;

        // Validate each image file exists and is readable
        foreach ($all_unconverted as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);

            if ($file_path && file_exists($file_path) && is_readable($file_path)) {
                $valid_count++;
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("🔍 INITIAL COUNT: Skipping image ID {$image->ID} - Invalid file: " . var_export($file_path, true));
                }
            }
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 ENHANCED BATCH CALCULATION: Database unconverted: ' . count($all_unconverted));
            error_log('🔧 ENHANCED BATCH CALCULATION: Valid files: ' . $valid_count);
        }

        return $valid_count;
    }

    /**
     * Get recent conversions with enhanced data for the Recent Conversions card
     * RECENT CONVERSIONS FIX: Always check database for existing conversions
     * LIMIT: Maximum 10 records to keep UI compact
     */
    public function get_recent_conversions($limit = 10, $offset = 0) {
        // CRITICAL: Enforce maximum limit of 10 records
        $limit = min($limit, 10);
        $all_conversions = array();

        // RECENT CONVERSIONS FIX: First get from new storage method
        $recent_conversions = get_option('redco_webp_recent_conversions', array());

        foreach ($recent_conversions as $conversion) {
            // ENHANCED FIX: Add missing fields for enhanced display
            $attachment_id = $conversion['attachment_id'];
            $file_path = redco_safe_get_attached_file($attachment_id);
            $filename = $file_path ? basename($file_path) : 'undefined';

            $all_conversions[] = array(
                'id' => $attachment_id,
                'title' => $conversion['title'],
                'filename' => $filename, // ENHANCED FIX: Add filename field
                'conversion_date' => $conversion['conversion_date'],
                'formatted_date' => human_time_diff(strtotime($conversion['conversion_date']), current_time('timestamp')) . ' ago',
                'original_size' => $conversion['original_size'],
                'webp_size' => $conversion['webp_size'],
                'formatted_original_size' => size_format($conversion['original_size']),
                'formatted_webp_size' => size_format($conversion['webp_size']),
                'savings' => $conversion['savings'],
                'formatted_savings' => size_format($conversion['savings']), // ENHANCED FIX: Add formatted savings
                'savings_percentage' => $conversion['savings_percentage'],
                'timestamp' => strtotime($conversion['conversion_date']),
                'thumbnail_url' => wp_get_attachment_thumb_url($attachment_id) // ENHANCED FIX: Add thumbnail
            );
        }

        // ALWAYS check database for existing conversions (including older ones)
        global $wpdb;

        $query = "
            SELECT p.ID, p.post_title, p.post_date, pm.meta_value as conversion_data
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
            AND (pm.meta_value LIKE '%s:9:\"converted\";b:1%' OR pm.meta_value LIKE '%\"converted\":true%')
            ORDER BY p.ID DESC
            LIMIT 50
        ";

        $db_conversions = $wpdb->get_results($query);

        // Get attachment IDs already in the new storage to avoid duplicates
        $existing_ids = array_column($recent_conversions, 'attachment_id');

        foreach ($db_conversions as $conversion) {
            // Skip if already in new storage
            if (in_array($conversion->ID, $existing_ids)) {
                continue;
            }

            $conversion_data = maybe_unserialize($conversion->meta_value);

            if (is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted']) {
                $original_size = $conversion_data['original_size'] ?? 0;
                $webp_size = $conversion_data['webp_size'] ?? 0;
                $savings = $original_size - $webp_size;
                $savings_percentage = $original_size > 0 ? round(($savings / $original_size) * 100, 1) : 0;

                // Get conversion date - prefer conversion_date from meta, fallback to post_date
                $conversion_date = $conversion_data['conversion_date'] ?? $conversion->post_date;

                // ENHANCED FIX: Add missing fields for enhanced display
                $file_path = redco_safe_get_attached_file($conversion->ID);
                $filename = $file_path ? basename($file_path) : 'undefined';

                // Add to all conversions array
                $all_conversions[] = array(
                    'id' => $conversion->ID,
                    'title' => $conversion->post_title ?: 'Untitled',
                    'filename' => $filename, // ENHANCED FIX: Add filename field
                    'conversion_date' => $conversion_date,
                    'formatted_date' => human_time_diff(strtotime($conversion_date), current_time('timestamp')) . ' ago',
                    'original_size' => $original_size,
                    'webp_size' => $webp_size,
                    'savings' => $savings,
                    'formatted_savings' => size_format($savings), // ENHANCED FIX: Add formatted savings
                    'savings_percentage' => $savings_percentage,
                    'formatted_original_size' => size_format($original_size),
                    'formatted_webp_size' => size_format($webp_size),
                    'timestamp' => strtotime($conversion_date),
                    'thumbnail_url' => wp_get_attachment_thumb_url($conversion->ID) // ENHANCED FIX: Add thumbnail
                );
            }
        }

        // RECENT CONVERSIONS FIX: Sort all conversions by timestamp (newest first)
        usort($all_conversions, function($a, $b) {
            return $b['timestamp'] - $a['timestamp'];
        });

        // Apply offset and limit
        $final_conversions = array_slice($all_conversions, $offset, $limit);

        return $final_conversions;
    }

    /**
     * Get total count of conversions for pagination
     * RECENT CONVERSIONS FIX: Count from both storage methods
     */
    public function get_conversions_count() {
        // Count from new storage method
        $recent_conversions = get_option('redco_webp_recent_conversions', array());
        $new_storage_count = count($recent_conversions);

        // Count from database (excluding duplicates) - FIXED: Use serialized format
        global $wpdb;
        $query = "
            SELECT COUNT(p.ID) as count
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
            AND (pm.meta_value LIKE '%s:9:\"converted\";b:1%' OR pm.meta_value LIKE '%\"converted\":true%')
        ";

        $db_count = intval($wpdb->get_var($query));

        // Get attachment IDs from new storage to avoid double counting
        $existing_ids = array_column($recent_conversions, 'attachment_id');

        if (!empty($existing_ids)) {
            // Subtract duplicates from database count
            $ids_placeholder = implode(',', array_fill(0, count($existing_ids), '%d'));
            $duplicate_query = "
                SELECT COUNT(p.ID) as count
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                WHERE p.post_type = 'attachment'
                AND pm.meta_key = '_webp_conversion_data'
                AND (pm.meta_value LIKE '%s:9:\"converted\";b:1%' OR pm.meta_value LIKE '%\"converted\":true%')
                AND p.ID IN ($ids_placeholder)
            ";

            $duplicate_count = intval($wpdb->get_var($wpdb->prepare($duplicate_query, $existing_ids)));
            $db_count -= $duplicate_count;
        }

        return $new_storage_count + $db_count;
    }

    /**
     * AJAX handler for getting recent conversions
     */
    public function ajax_get_recent_conversions() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats')) {
            wp_send_json_error('Security check failed');
            return;
        }

        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
        $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;

        $recent_conversions = $this->get_recent_conversions($limit, $offset);

        wp_send_json_success($recent_conversions);
    }

    /**
     * REAL-TIME STATS: Trigger statistics refresh when new attachment is added
     */
    public function trigger_stats_refresh_on_upload($attachment_id) {
        // Only process image attachments
        $mime_type = get_post_mime_type($attachment_id);
        if (!$mime_type || strpos($mime_type, 'image/') !== 0) {
            return;
        }

        // Check if it's a convertible image type
        if (!in_array($mime_type, ['image/jpeg', 'image/jpg', 'image/png'])) {
            return;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('📊 REAL-TIME STATS: New convertible image uploaded, ID: ' . $attachment_id . ', Type: ' . $mime_type);
        }

        // Schedule stats refresh for after upload completion
        wp_schedule_single_event(time() + 2, 'redco_webp_delayed_stats_refresh', array($attachment_id));
    }

    /**
     * REAL-TIME STATS: Trigger statistics refresh after metadata generation (thumbnails complete)
     */
    public function trigger_stats_refresh_after_metadata($metadata, $attachment_id) {
        // Only process image attachments
        $mime_type = get_post_mime_type($attachment_id);
        if (!$mime_type || strpos($mime_type, 'image/') !== 0) {
            return $metadata;
        }

        // Check if it's a convertible image type
        if (!in_array($mime_type, ['image/jpeg', 'image/jpg', 'image/png'])) {
            return $metadata;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('📊 REAL-TIME STATS: Metadata generated for image ID: ' . $attachment_id . ', triggering stats refresh');
        }

        // Clear any cached totals to force recalculation
        $this->clear_cached_stats();

        // Trigger immediate stats refresh via JavaScript
        $this->schedule_frontend_stats_refresh();

        return $metadata;
    }

    /**
     * REAL-TIME STATS: Clear cached statistics to force recalculation
     * CRITICAL FIX: Made public so it can be called from AJAX handlers
     */
    public function clear_cached_stats() {
        // Force recalculation by calling get_cached_initial_total with reset flag
        $this->reset_cached_total();

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('📊 REAL-TIME STATS: Cleared cached statistics');
        }
    }

    /**
     * REAL-TIME STATS: Reset the cached total to force recalculation
     */
    private function reset_cached_total() {
        // Call get_cached_initial_total with a special reset parameter
        $this->get_cached_initial_total(true);
    }

    /**
     * REAL-TIME STATS: Schedule frontend statistics refresh
     */
    private function schedule_frontend_stats_refresh() {
        // Set a transient that the frontend JavaScript can check
        set_transient('redco_webp_stats_refresh_needed', time(), 60);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('📊 REAL-TIME STATS: Scheduled frontend stats refresh');
        }
    }

    /**
     * REAL-TIME STATS: AJAX handler for refreshing statistics
     */
    public function ajax_refresh_stats() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats')) {
            wp_send_json_error('Security check failed');
            return;
        }

        try {
            // Clear cached statistics to force fresh calculation
            $this->clear_cached_stats();

            // Get fresh statistics using the same logic as bulk conversion
            $stats = $this->get_stats();

            // Clear the refresh flag
            delete_transient('redco_webp_stats_refresh_needed');

            wp_send_json_success(array(
                'stats' => $stats,
                'message' => 'Statistics refreshed successfully',
                'timestamp' => current_time('mysql')
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to refresh statistics: ' . $e->getMessage());
        }
    }

    /**
     * REAL-TIME STATS: AJAX handler to check if statistics refresh is needed
     */
    public function ajax_check_refresh_needed() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_stats')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check if refresh is needed
        $refresh_needed = get_transient('redco_webp_stats_refresh_needed');

        wp_send_json_success(array(
            'refresh_needed' => !empty($refresh_needed),
            'timestamp' => $refresh_needed ? $refresh_needed : null
        ));
    }

    // REMOVED: Debug test media library method - not needed in production

    // REMOVED: Duplicate ajax_bulk_convert method - original exists at line 817

    // REMOVED: Duplicate ajax_get_stats method - handled by global handlers

    // REMOVED: Duplicate ajax_test_conversion method - handled by global handlers

    /**
     * AJAX handler for rollback conversion (transferred from standard class)
     */
    public function ajax_rollback_conversion() {
        if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_rollback') || !current_user_can('manage_options')) {
            wp_die(__('Security check failed', 'redco-optimizer'));
        }

        $attachment_id = intval($_POST['attachment_id']);
        $result = $this->rollback_conversion($attachment_id);

        wp_send_json($result);
    }

    /**
     * Rollback WebP conversion for a specific attachment (transferred from standard class)
     */
    public function rollback_conversion($attachment_id) {
        $file_path = redco_safe_get_attached_file($attachment_id);
        if (!$file_path || !file_exists($file_path)) {
            return array(
                'success' => false,
                'message' => __('Original file not found', 'redco-optimizer')
            );
        }

        $webp_path = $this->get_webp_path($file_path);
        $success = true;
        $message = __('Rollback completed successfully', 'redco-optimizer');

        // Delete WebP file if it exists
        if ($webp_path && file_exists($webp_path)) {
            if (!@unlink($webp_path)) {
                $success = false;
                $message = __('Failed to delete WebP file', 'redco-optimizer');
            }
        }

        // Remove conversion metadata
        delete_post_meta($attachment_id, '_webp_conversion_data');

        return array(
            'success' => $success,
            'message' => $message
        );
    }

    // REMOVED: override_attached_file_for_webp method caused infinite loops and memory exhaustion
}

// TARGETED FIX: Since URLs are working, just fix the filename display
function redco_webp_fix_media_library_filename($response, $attachment, $meta) {
    // FATAL ERROR FIX: Only process image attachments with safe check
    $mime_type = get_post_mime_type($attachment->ID);
    if (!$mime_type || strpos($mime_type, 'image/') !== 0) {
        return $response;
    }

    // Check if this image has been converted to WebP
    $conversion_data = get_post_meta($attachment->ID, '_webp_conversion_data', true);

    if ($conversion_data && isset($conversion_data['converted']) && $conversion_data['converted']) {
        // AGGRESSIVE: Fix ALL filename-related fields
        if (isset($response['filename'])) {
            $response['filename'] = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $response['filename']);
        }

        if (isset($response['title'])) {
            $response['title'] = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $response['title']);
        }

        if (isset($response['name'])) {
            $response['name'] = preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $response['name']);
        }

        // Add WebP indicator
        $response['webp_converted'] = true;
        $response['webp_filename_fixed'] = true;
    }

    return $response;
}

// REMOVED: This hook was causing Media Library blank page issues
// add_filter('wp_prepare_attachment_for_js', 'redco_webp_fix_media_library_filename', PHP_INT_MAX, 3);

// Remove the duplicate initialization - it's now handled in the main file
