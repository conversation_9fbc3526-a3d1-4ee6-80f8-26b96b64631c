<?php
/**
 * Quick WebP Test - Check if WebP conversion is working
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only run in admin for administrators
if (!is_admin() || !current_user_can('manage_options')) {
    return;
}

echo "<h2>🚀 Quick WebP Test</h2>";

// 1. Check if WebP module is enabled
echo "<h3>1. Module Status</h3>";
$module_enabled = redco_is_module_enabled('smart-webp-conversion');
echo "<p>WebP module enabled: " . ($module_enabled ? "✅ YES" : "❌ NO") . "</p>";

if (!$module_enabled) {
    echo "<p style='color: red;'>❌ WebP module is disabled. Enable it first!</p>";
    return;
}

// 2. Check WebP class availability
echo "<h3>2. Class Availability</h3>";
$class_exists = class_exists('Redco_Smart_WebP_Conversion');
echo "<p>WebP class available: " . ($class_exists ? "✅ YES" : "❌ NO") . "</p>";

if (!$class_exists) {
    echo "<p style='color: red;'>❌ WebP class not loaded. Check file paths!</p>";
    return;
}

// 3. Create WebP instance
echo "<h3>3. Instance Creation</h3>";
try {
    $webp_instance = new Redco_Smart_WebP_Conversion();
    echo "<p>WebP instance created: ✅ YES</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Failed to create WebP instance: " . $e->getMessage() . "</p>";
    return;
}

// 4. Check server support
echo "<h3>4. Server Support</h3>";
$server_support = $webp_instance->can_convert_webp();
echo "<p>Server supports WebP: " . ($server_support ? "✅ YES" : "❌ NO") . "</p>";

if (!$server_support) {
    echo "<p style='color: red;'>❌ Server doesn't support WebP conversion!</p>";
}

// 5. Check for images in database
echo "<h3>5. Database Check</h3>";
global $wpdb;

$total_images = $wpdb->get_var("
    SELECT COUNT(*)
    FROM {$wpdb->posts}
    WHERE post_type = 'attachment'
    AND post_mime_type IN ('image/jpeg', 'image/png', 'image/gif')
");

echo "<p>Total images in database: {$total_images}</p>";

$converted_images = $wpdb->get_var("
    SELECT COUNT(*)
    FROM {$wpdb->postmeta}
    WHERE meta_key = '_webp_conversion_data'
    AND meta_value LIKE '%\"converted\":true%'
");

echo "<p>Converted images: {$converted_images}</p>";

// 6. Test conversion on one image
if ($total_images > 0 && $converted_images == 0) {
    echo "<h3>6. Test Conversion</h3>";
    
    // Get one image to test
    $test_image = $wpdb->get_row("
        SELECT p.ID, p.post_title
        FROM {$wpdb->posts} p
        WHERE p.post_type = 'attachment'
        AND p.post_mime_type IN ('image/jpeg', 'image/png', 'image/gif')
        LIMIT 1
    ");
    
    if ($test_image) {
        echo "<p>Testing conversion on: {$test_image->post_title} (ID: {$test_image->ID})</p>";
        
        // Get file path
        $file_path = get_attached_file($test_image->ID);
        echo "<p>File path: {$file_path}</p>";
        
        if ($file_path && file_exists($file_path)) {
            echo "<p>File exists: ✅ YES</p>";
            
            // Test if convertible
            $is_convertible = $webp_instance->is_convertible_image($file_path);
            echo "<p>Is convertible: " . ($is_convertible ? "✅ YES" : "❌ NO") . "</p>";
            
            if ($is_convertible && $server_support) {
                echo "<p>🔄 Attempting conversion...</p>";
                
                try {
                    $result = $webp_instance->convert_image_to_webp($test_image->ID);
                    
                    if ($result['success']) {
                        echo "<p style='color: green;'>✅ Conversion successful!</p>";
                        echo "<p>WebP path: {$result['webp_path']}</p>";
                        echo "<p>Original size: " . size_format($result['original_size']) . "</p>";
                        echo "<p>WebP size: " . size_format($result['webp_size']) . "</p>";
                        echo "<p>Savings: " . $result['savings_percentage'] . "%</p>";
                    } else {
                        echo "<p style='color: red;'>❌ Conversion failed: {$result['error']}</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Conversion error: " . $e->getMessage() . "</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>❌ File does not exist: {$file_path}</p>";
        }
    }
}

// 7. Test URL filtering if we have converted images
if ($converted_images > 0) {
    echo "<h3>7. URL Filtering Test</h3>";
    
    $converted_image = $wpdb->get_row("
        SELECT p.ID, p.post_title
        FROM {$wpdb->posts} p
        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'attachment'
        AND pm.meta_key = '_webp_conversion_data'
        AND pm.meta_value LIKE '%\"converted\":true%'
        LIMIT 1
    ");
    
    if ($converted_image) {
        echo "<p>Testing URL filtering on: {$converted_image->post_title} (ID: {$converted_image->ID})</p>";
        
        // Test URL filtering
        $original_url = wp_get_attachment_url($converted_image->ID);
        echo "<p>URL: {$original_url}</p>";
        
        $has_webp = strpos($original_url, '.webp') !== false;
        echo "<p>URL contains .webp: " . ($has_webp ? "✅ YES" : "❌ NO") . "</p>";
        
        if (!$has_webp) {
            echo "<p style='color: orange;'>⚠️ URL filtering may not be working. Check hook registration.</p>";
        }
    }
}

echo "<h3>8. Summary</h3>";
if ($module_enabled && $class_exists && $server_support) {
    if ($converted_images > 0) {
        echo "<p style='color: green;'>✅ WebP conversion is working! You have {$converted_images} converted images.</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ WebP conversion is ready but no images converted yet. Try the bulk conversion tool.</p>";
    }
} else {
    echo "<p style='color: red;'>❌ WebP conversion has issues. Check the problems above.</p>";
}
