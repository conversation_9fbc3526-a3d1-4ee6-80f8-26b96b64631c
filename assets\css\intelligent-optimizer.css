/**
 * Intelligent Optimizer Styles
 * Styles for the intelligent optimization wizard interface
 */

/* Modal Overlay */
.optimizer-modal-overlay,
.preview-modal-overlay,
.success-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

/* Modal Container */
.optimizer-modal,
.preview-modal,
.success-modal {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 900px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(-20px);
    transition: all 0.3s ease;
}

.optimizer-modal-overlay.active,
.preview-modal-overlay.active,
.success-modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.optimizer-modal.active,
.preview-modal.active,
.success-modal.active {
    transform: scale(1) translateY(0);
}

/* Modal Header */
.optimizer-modal-header,
.preview-modal-header,
.success-modal-header {
    background: #0073aa;
    color: #fff;
    padding: 20px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #ddd;
}

.optimizer-modal-header h2,
.preview-modal-header h2,
.success-modal-header h2 {
    margin: 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.optimizer-modal-close,
.preview-modal-close {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.optimizer-modal-close:hover,
.preview-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Modal Content */
.optimizer-modal-content,
.preview-modal-content,
.success-modal-content {
    padding: 25px;
    max-height: calc(90vh - 140px);
    overflow-y: auto;
}

/* Optimizer Sections */
.optimizer-section {
    margin-bottom: 30px;
    padding-bottom: 25px;
    border-bottom: 1px solid #eee;
}

.optimizer-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.optimizer-section h3 {
    margin: 0 0 20px 0;
    font-size: 16px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Performance Score Display */
.performance-score-display {
    display: flex;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
}

.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 6px solid;
    position: relative;
    flex-shrink: 0;
}

.score-circle.grade-aplus,
.score-circle.grade-a {
    border-color: #46b450;
    background: linear-gradient(135deg, #46b450, #5cbf60);
    color: #fff;
}

.score-circle.grade-b {
    border-color: #ffb900;
    background: linear-gradient(135deg, #ffb900, #ffc426);
    color: #fff;
}

.score-circle.grade-c {
    border-color: #ff8c00;
    background: linear-gradient(135deg, #ff8c00, #ffa726);
    color: #fff;
}

.score-circle.grade-d,
.score-circle.grade-f {
    border-color: #dc3232;
    background: linear-gradient(135deg, #dc3232, #e74c3c);
    color: #fff;
}

.score-value {
    font-size: 32px;
    font-weight: bold;
    line-height: 1;
}

.score-grade {
    font-size: 14px;
    font-weight: 600;
    margin-top: 4px;
}

.score-details {
    flex: 1;
    min-width: 300px;
}

.score-breakdown {
    margin-bottom: 15px;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.score-item:last-child {
    border-bottom: none;
}

.score-label {
    color: #666;
    font-size: 14px;
}

.score-item .score-value {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.modules-status {
    background: #f8f9fa;
    padding: 12px 15px;
    border-radius: 5px;
    border-left: 4px solid #0073aa;
    font-size: 14px;
    color: #666;
}

/* Recommendations List */
.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.recommendation-item {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    background: #fff;
    transition: all 0.2s ease;
}

.recommendation-item:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
}

.recommendation-item.disabled {
    opacity: 0.6;
    background: #f9f9f9;
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    gap: 15px;
}

.recommendation-checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    flex: 1;
}

.recommendation-checkbox {
    margin: 0 !important;
}

.recommendation-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
}

.recommendation-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

.priority-badge,
.impact-badge,
.improvement-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-badge.priority-high {
    background: #dc3232;
    color: #fff;
}

.priority-badge.priority-medium {
    background: #ffb900;
    color: #fff;
}

.priority-badge.priority-low {
    background: #666;
    color: #fff;
}

.impact-badge.impact-high {
    background: #46b450;
    color: #fff;
}

.impact-badge.impact-medium {
    background: #0073aa;
    color: #fff;
}

.impact-badge.impact-low {
    background: #82878c;
    color: #fff;
}

.improvement-badge {
    background: #e8f5e8;
    color: #46b450;
    border: 1px solid #46b450;
}

.recommendation-content {
    color: #666;
    line-height: 1.5;
}

.recommendation-description {
    margin: 0 0 10px 0;
    font-size: 14px;
}

.recommendation-reason {
    margin: 0;
    font-size: 13px;
    color: #777;
}

.recommendation-warning {
    margin: 10px 0 0 0;
    padding: 8px 12px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    color: #856404;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Improvement Display */
.improvement-display {
    display: flex;
    align-items: center;
    gap: 25px;
    flex-wrap: wrap;
}

.improvement-percentage {
    text-align: center;
    flex-shrink: 0;
}

.improvement-value {
    display: block;
    font-size: 36px;
    font-weight: bold;
    color: #46b450;
    line-height: 1;
}

.improvement-label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.improvement-description {
    flex: 1;
    min-width: 250px;
}

.improvement-description p {
    margin: 0 0 8px 0;
    color: #666;
    line-height: 1.5;
}

.improvement-timeframe {
    font-size: 13px;
    color: #777;
    font-style: italic;
}

/* Modal Footer */
.optimizer-modal-footer,
.preview-modal-footer,
.success-modal-footer {
    background: #f8f9fa;
    padding: 20px 25px;
    border-top: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

.optimizer-actions {
    display: flex;
    gap: 10px;
}

.selected-count {
    font-size: 14px;
    color: #666;
}

/* Error Notice */
.optimizer-error-notice {
    margin-bottom: 20px;
    border-left: 4px solid #dc3232;
}

/* Score Comparison (Preview Modal) */
.score-comparison {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.score-before,
.score-after {
    text-align: center;
}

.score-before h4,
.score-after h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.score-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: #0073aa;
}

.score-arrow .dashicons {
    font-size: 24px;
}

.improvement-indicator {
    background: #e8f5e8;
    color: #46b450;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

/* Preview Summary */
.preview-summary h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
}

.changes-list {
    list-style: none;
    margin: 0 0 20px 0;
    padding: 0;
}

.change-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    color: #666;
    font-size: 14px;
}

.change-item .dashicons {
    color: #46b450;
    font-size: 16px;
}

.summary-stats {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    min-width: 120px;
}

.stat-value {
    display: block;
    font-size: 20px;
    font-weight: bold;
    color: #0073aa;
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Success Modal */
.success-summary {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: #e8f5e8;
    border-radius: 6px;
    border: 1px solid #46b450;
}

.success-icon {
    width: 60px;
    height: 60px;
    background: #46b450;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.success-icon .dashicons {
    color: #fff;
    font-size: 24px;
}

.success-message h3 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 18px;
}

.success-message p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.new-score-display {
    text-align: center;
    margin-bottom: 30px;
}

.new-score-display h4 {
    margin: 0 0 20px 0;
    font-size: 16px;
    color: #333;
}

.score-improvement {
    margin: 15px 0 0 0;
    color: #666;
    font-size: 14px;
}

.errors-section {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.errors-section h4 {
    margin: 0 0 15px 0;
    color: #856404;
    font-size: 16px;
}

.errors-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.error-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    color: #856404;
    font-size: 14px;
}

.error-item .dashicons {
    color: #dc3232;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .optimizer-modal,
    .preview-modal,
    .success-modal {
        width: 95%;
        max-height: 95vh;
    }
    
    .performance-score-display,
    .improvement-display,
    .score-comparison {
        flex-direction: column;
        text-align: center;
    }
    
    .recommendation-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .recommendation-badges {
        justify-content: flex-start;
    }
    
    .optimizer-modal-footer,
    .preview-modal-footer,
    .success-modal-footer {
        flex-direction: column;
        gap: 15px;
    }
    
    .optimizer-actions {
        width: 100%;
        justify-content: center;
    }
    
    .summary-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .success-summary {
        flex-direction: column;
        text-align: center;
    }
}
