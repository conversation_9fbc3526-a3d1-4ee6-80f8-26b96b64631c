<?php
/**
 * Simple URL Filtering Test
 * 
 * This script tests if URL filtering is working for WebP images
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only run in admin for administrators
if (!is_admin() || !current_user_can('manage_options')) {
    return;
}

echo "<h2>🔍 URL Filtering Test</h2>";

// Check if we have any converted images
global $wpdb;

$converted_images = $wpdb->get_results("
    SELECT p.ID, p.post_title, pm.meta_value
    FROM {$wpdb->posts} p
    INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
    WHERE p.post_type = 'attachment'
    AND pm.meta_key = '_webp_conversion_data'
    AND pm.meta_value LIKE '%\"converted\":true%'
    LIMIT 3
");

echo "<p>Found " . count($converted_images) . " converted images</p>";

if (empty($converted_images)) {
    echo "<p style='color: red;'>❌ No converted images found! Please convert some images first.</p>";
    
    // Show some unconverted images for testing
    $unconverted_images = $wpdb->get_results("
        SELECT p.ID, p.post_title
        FROM {$wpdb->posts} p
        WHERE p.post_type = 'attachment'
        AND p.post_mime_type IN ('image/jpeg', 'image/png', 'image/gif')
        LIMIT 3
    ");
    
    echo "<h3>Available images for conversion:</h3>";
    foreach ($unconverted_images as $image) {
        echo "<p>ID: {$image->ID} - {$image->post_title}</p>";
    }
    
    return;
}

// Test URL filtering for each converted image
foreach ($converted_images as $image) {
    $attachment_id = $image->ID;
    $conversion_data = maybe_unserialize($image->meta_value);
    
    echo "<h3>Testing Attachment ID: {$attachment_id}</h3>";
    echo "<p>Title: {$image->post_title}</p>";
    
    // Test wp_get_attachment_url
    $original_url = wp_get_attachment_url($attachment_id);
    echo "<p>wp_get_attachment_url: {$original_url}</p>";
    
    // Test wp_get_attachment_image_src
    $image_src = wp_get_attachment_image_src($attachment_id, 'full');
    if ($image_src) {
        echo "<p>wp_get_attachment_image_src: {$image_src[0]}</p>";
    }
    
    // Test wp_prepare_attachment_for_js
    $attachment_obj = get_post($attachment_id);
    $js_data = wp_prepare_attachment_for_js($attachment_obj);
    if ($js_data && isset($js_data['url'])) {
        echo "<p>wp_prepare_attachment_for_js URL: {$js_data['url']}</p>";
    }
    
    // Check if URLs contain .webp
    $has_webp_url = strpos($original_url, '.webp') !== false;
    $has_webp_src = $image_src && strpos($image_src[0], '.webp') !== false;
    $has_webp_js = $js_data && isset($js_data['url']) && strpos($js_data['url'], '.webp') !== false;
    
    echo "<p>Results:</p>";
    echo "<ul>";
    echo "<li>wp_get_attachment_url has WebP: " . ($has_webp_url ? "✅ YES" : "❌ NO") . "</li>";
    echo "<li>wp_get_attachment_image_src has WebP: " . ($has_webp_src ? "✅ YES" : "❌ NO") . "</li>";
    echo "<li>wp_prepare_attachment_for_js has WebP: " . ($has_webp_js ? "✅ YES" : "❌ NO") . "</li>";
    echo "</ul>";
    
    // Check if WebP file exists
    if (isset($conversion_data['webp_path'])) {
        $webp_exists = file_exists($conversion_data['webp_path']);
        echo "<p>WebP file exists: " . ($webp_exists ? "✅ YES" : "❌ NO") . "</p>";
        if ($webp_exists) {
            echo "<p>WebP path: {$conversion_data['webp_path']}</p>";
        }
    }
    
    echo "<hr>";
}

// Test hook registration
echo "<h3>Hook Registration Test</h3>";

// FIXED: Use correct global variable name
$webp_converter = $GLOBALS['redco_webp_instance'] ?? null;

if (!$webp_converter) {
    echo "<p style='color: red;'>❌ WebP converter not found in globals (redco_webp_instance)</p>";

    // Try to create instance manually
    if (class_exists('Redco_Smart_WebP_Conversion')) {
        echo "<p>Trying to create WebP instance manually...</p>";
        $webp_converter = new Redco_Smart_WebP_Conversion();
        echo "<p style='color: green;'>✅ WebP converter created manually</p>";
    } else {
        echo "<p style='color: red;'>❌ WebP class not available</p>";
    }
} else {
    echo "<p style='color: green;'>✅ WebP converter found in globals</p>";
}

if ($webp_converter) {
    // Test hook registration
    $hooks = array(
        'wp_get_attachment_url' => 'serve_webp_url_in_media_library',
        'wp_get_attachment_thumb_url' => 'serve_webp_url_in_media_library',
        'wp_prepare_attachment_for_js' => 'fix_media_library_webp_urls'
    );

    foreach ($hooks as $hook => $method) {
        $registered = has_filter($hook, array($webp_converter, $method));
        echo "<p>{$hook}: " . ($registered !== false ? "✅ Registered (priority: {$registered})" : "❌ Not registered") . "</p>";
    }
}

// Manual test
if (!empty($converted_images) && $webp_converter) {
    echo "<h3>Manual Method Test</h3>";
    
    $test_attachment_id = $converted_images[0]->ID;
    $test_url = "http://example.com/test.jpg";
    
    if (method_exists($webp_converter, 'serve_webp_url_in_media_library')) {
        echo "<p>Testing serve_webp_url_in_media_library method directly...</p>";
        $result = $webp_converter->serve_webp_url_in_media_library($test_url, $test_attachment_id);
        echo "<p>Input: {$test_url}</p>";
        echo "<p>Output: {$result}</p>";
        echo "<p>Method working: " . ($test_url !== $result ? "✅ YES" : "❌ NO") . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Method serve_webp_url_in_media_library not found</p>";
    }
}
