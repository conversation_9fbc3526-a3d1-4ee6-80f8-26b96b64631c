<?php
/**
 * Debug URL Filtering - Check why local paths are being served instead of URLs
 */

// Load WordPress
require_once('../../../../../../wp-load.php');

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🔍 Debug URL Filtering Issue</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .warning { color: orange; } .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; }</style>";

// Get the specific image from the screenshot (ID should be visible in the URL)
$attachment_id = 15; // This appears to be the ID from the screenshot URL

echo "<h2>Testing Attachment ID: {$attachment_id}</h2>";

// 1. Check if this image exists
$attachment = get_post($attachment_id);
if (!$attachment) {
    echo "<p class='error'>❌ Attachment ID {$attachment_id} not found</p>";
    exit;
}

echo "<p>Image Title: {$attachment->post_title}</p>";

// 2. Check conversion data
$conversion_data = get_post_meta($attachment_id, '_webp_conversion_data', true);
echo "<h3>Conversion Data:</h3>";
if ($conversion_data) {
    echo "<div class='code'>";
    echo "Converted: " . (isset($conversion_data['converted']) ? ($conversion_data['converted'] ? 'YES' : 'NO') : 'NOT SET') . "\n";
    echo "webp_path: " . (isset($conversion_data['webp_path']) ? $conversion_data['webp_path'] : 'NOT SET') . "\n";
    echo "webp_url: " . (isset($conversion_data['webp_url']) ? $conversion_data['webp_url'] : 'NOT SET') . "\n";
    echo "</div>";
} else {
    echo "<p class='warning'>⚠️ No conversion data found</p>";
}

// 3. Test WordPress core URL functions
echo "<h3>WordPress Core URL Functions:</h3>";

// Test wp_get_attachment_url (this is what should be filtered)
$core_url = wp_get_attachment_url($attachment_id);
echo "<p><strong>wp_get_attachment_url:</strong> {$core_url}</p>";

// Check if it's a local path or URL
$is_local_path = (strpos($core_url, 'D:') === 0 || strpos($core_url, 'C:') === 0 || strpos($core_url, '/') === 0);
$is_webp = strpos($core_url, '.webp') !== false;

echo "<p>Is local path: " . ($is_local_path ? "<span class='error'>❌ YES</span>" : "<span class='success'>✅ NO</span>") . "</p>";
echo "<p>Is WebP: " . ($is_webp ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";

// 4. Test hook registration
echo "<h3>Hook Registration Check:</h3>";

// Check if our WebP instance exists
$webp_instance = $GLOBALS['redco_webp_instance'] ?? null;
if ($webp_instance) {
    echo "<p class='success'>✅ WebP instance found in globals</p>";
    
    // Check specific hooks
    $url_hook = has_filter('wp_get_attachment_url', array($webp_instance, 'serve_webp_url_in_media_library'));
    echo "<p>wp_get_attachment_url hook: " . ($url_hook !== false ? "<span class='success'>✅ Registered (priority: {$url_hook})</span>" : "<span class='error'>❌ Not registered</span>") . "</p>";
    
} else {
    echo "<p class='error'>❌ WebP instance not found in globals</p>";
}

// 5. Manual URL filtering test
echo "<h3>Manual URL Filtering Test:</h3>";

if ($webp_instance && method_exists($webp_instance, 'serve_webp_url_in_media_library')) {
    echo "<p>Testing serve_webp_url_in_media_library method directly...</p>";
    
    // Test with the actual URL
    $filtered_url = $webp_instance->serve_webp_url_in_media_library($core_url, $attachment_id);
    
    echo "<p><strong>Input URL:</strong> {$core_url}</p>";
    echo "<p><strong>Filtered URL:</strong> {$filtered_url}</p>";
    
    if ($core_url !== $filtered_url) {
        echo "<p class='success'>✅ URL filtering is working - URLs are different</p>";
        
        $filtered_is_webp = strpos($filtered_url, '.webp') !== false;
        $filtered_is_url = (strpos($filtered_url, 'http') === 0);
        
        echo "<p>Filtered URL is WebP: " . ($filtered_is_webp ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
        echo "<p>Filtered URL is web URL: " . ($filtered_is_url ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
        
    } else {
        echo "<p class='error'>❌ URL filtering is NOT working - URLs are identical</p>";
    }
} else {
    echo "<p class='error'>❌ Cannot test - method not available</p>";
}

// 6. Check original file and WebP file
echo "<h3>File System Check:</h3>";

$original_file = get_attached_file($attachment_id);
echo "<p><strong>Original file path:</strong> {$original_file}</p>";
echo "<p>Original file exists: " . (file_exists($original_file) ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";

if ($conversion_data && isset($conversion_data['webp_path'])) {
    $webp_path = $conversion_data['webp_path'];
    echo "<p><strong>WebP file path:</strong> {$webp_path}</p>";
    echo "<p>WebP file exists: " . (file_exists($webp_path) ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
}

// 7. Test URL generation manually
echo "<h3>Manual URL Generation Test:</h3>";

if ($original_file) {
    $upload_dir = wp_upload_dir();
    echo "<p><strong>Upload base dir:</strong> {$upload_dir['basedir']}</p>";
    echo "<p><strong>Upload base URL:</strong> {$upload_dir['baseurl']}</p>";
    
    // Generate correct URL manually
    $correct_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $original_file);
    $correct_url = wp_normalize_path($correct_url);
    $correct_url = str_replace('\\', '/', $correct_url);
    
    echo "<p><strong>Manually generated URL:</strong> {$correct_url}</p>";
    
    $manual_is_url = (strpos($correct_url, 'http') === 0);
    echo "<p>Manual URL is web URL: " . ($manual_is_url ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
}

// 8. Diagnosis
echo "<h2>🎯 Diagnosis</h2>";

if ($is_local_path) {
    echo "<p class='error'><strong>PROBLEM CONFIRMED:</strong> WordPress is serving local file paths instead of URLs!</p>";
    
    if ($url_hook === false) {
        echo "<p class='error'><strong>ROOT CAUSE:</strong> URL filtering hook is not registered!</p>";
        echo "<p><strong>SOLUTION:</strong> The WebP URL filtering hooks are not working. Need to check hook registration.</p>";
    } else {
        echo "<p class='warning'><strong>POSSIBLE CAUSE:</strong> URL filtering hook is registered but not working properly.</p>";
        echo "<p><strong>SOLUTION:</strong> Check the URL filtering method implementation.</p>";
    }
} else {
    echo "<p class='success'>✅ URLs are being served correctly</p>";
}

echo "<h3>🔧 Immediate Actions:</h3>";
echo "<ol>";
echo "<li><strong>Check Hook Registration:</strong> Ensure WebP URL filtering hooks are properly registered</li>";
echo "<li><strong>Test URL Filtering:</strong> Verify the filtering methods are being called</li>";
echo "<li><strong>Check Conversion Data:</strong> Ensure WebP URLs are stored correctly</li>";
echo "<li><strong>Re-convert Image:</strong> Try converting this specific image again</li>";
echo "</ol>";
