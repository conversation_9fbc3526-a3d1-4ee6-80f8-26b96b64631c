<?php
/**
 * Deep Debug - Find the root cause of local paths being served
 */

// Load WordPress
require_once('../../../../../../wp-load.php');

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🔍 Deep Debug - Root Cause Analysis</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .warning { color: orange; } .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; }</style>";

$attachment_id = 15; // The problematic image

echo "<h2>Investigating Attachment ID: {$attachment_id}</h2>";

// 1. Check WordPress core metadata
echo "<h3>1. WordPress Core Metadata Analysis</h3>";

$attached_file = get_post_meta($attachment_id, '_wp_attached_file', true);
echo "<p><strong>_wp_attached_file:</strong> {$attached_file}</p>";

$attachment_metadata = get_post_meta($attachment_id, '_wp_attachment_metadata', true);
echo "<p><strong>_wp_attachment_metadata:</strong></p>";
echo "<div class='code'>" . print_r($attachment_metadata, true) . "</div>";

// 2. Check upload directory configuration
echo "<h3>2. Upload Directory Configuration</h3>";

$upload_dir = wp_upload_dir();
echo "<div class='code'>";
echo "basedir: " . $upload_dir['basedir'] . "\n";
echo "baseurl: " . $upload_dir['baseurl'] . "\n";
echo "path: " . $upload_dir['path'] . "\n";
echo "url: " . $upload_dir['url'] . "\n";
echo "subdir: " . $upload_dir['subdir'] . "\n";
echo "</div>";

// 3. Test WordPress core functions step by step
echo "<h3>3. WordPress Core Function Analysis</h3>";

// Test get_attached_file
$file_path = get_attached_file($attachment_id);
echo "<p><strong>get_attached_file():</strong> {$file_path}</p>";

// Test wp_get_attachment_url WITHOUT our filters
echo "<h4>Testing wp_get_attachment_url without our filters:</h4>";

// Temporarily remove our filters
global $wp_filter;
$original_filters = array();

$hooks_to_remove = array('wp_get_attachment_url', 'wp_get_attachment_thumb_url', 'wp_get_attachment_image_url');
foreach ($hooks_to_remove as $hook) {
    if (isset($wp_filter[$hook])) {
        $original_filters[$hook] = $wp_filter[$hook];
        unset($wp_filter[$hook]);
    }
}

$core_url_no_filters = wp_get_attachment_url($attachment_id);
echo "<p><strong>wp_get_attachment_url (no filters):</strong> {$core_url_no_filters}</p>";

// Restore our filters
foreach ($original_filters as $hook => $filter_data) {
    $wp_filter[$hook] = $filter_data;
}

// Test with our filters
$core_url_with_filters = wp_get_attachment_url($attachment_id);
echo "<p><strong>wp_get_attachment_url (with filters):</strong> {$core_url_with_filters}</p>";

// 4. Check if the issue is in WordPress core URL generation
echo "<h3>4. Manual URL Generation Test</h3>";

if ($attached_file) {
    // Method 1: Using upload_dir
    $manual_url_1 = $upload_dir['baseurl'] . '/' . $attached_file;
    echo "<p><strong>Manual URL (method 1):</strong> {$manual_url_1}</p>";
    
    // Method 2: Using str_replace
    if ($file_path) {
        $manual_url_2 = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $file_path);
        $manual_url_2 = wp_normalize_path($manual_url_2);
        $manual_url_2 = str_replace('\\', '/', $manual_url_2);
        echo "<p><strong>Manual URL (method 2):</strong> {$manual_url_2}</p>";
    }
}

// 5. Check WordPress options that might affect URL generation
echo "<h3>5. WordPress Configuration Check</h3>";

$site_url = get_option('siteurl');
$home_url = get_option('home');
$upload_path = get_option('upload_path');
$upload_url_path = get_option('upload_url_path');

echo "<div class='code'>";
echo "siteurl: {$site_url}\n";
echo "home: {$home_url}\n";
echo "upload_path: {$upload_path}\n";
echo "upload_url_path: {$upload_url_path}\n";
echo "</div>";

// 6. Check if there are other plugins interfering
echo "<h3>6. Plugin Interference Check</h3>";

// Get all filters on wp_get_attachment_url
global $wp_filter;
if (isset($wp_filter['wp_get_attachment_url'])) {
    echo "<p><strong>Filters on wp_get_attachment_url:</strong></p>";
    echo "<div class='code'>";
    foreach ($wp_filter['wp_get_attachment_url']->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (is_array($callback['function'])) {
                $class = is_object($callback['function'][0]) ? get_class($callback['function'][0]) : $callback['function'][0];
                echo "Priority {$priority}: {$class}::{$callback['function'][1]}\n";
            } else {
                echo "Priority {$priority}: {$callback['function']}\n";
            }
        }
    }
    echo "</div>";
}

// 7. Test the actual problem - why is WordPress returning local paths?
echo "<h3>7. Root Cause Investigation</h3>";

// Check if _wp_attached_file contains full path instead of relative path
$is_attached_file_full_path = (strpos($attached_file, $upload_dir['basedir']) === 0);
echo "<p>_wp_attached_file contains full path: " . ($is_attached_file_full_path ? "<span class='error'>❌ YES - This is the problem!</span>" : "<span class='success'>✅ NO</span>") . "</p>";

if ($is_attached_file_full_path) {
    echo "<div class='error'>";
    echo "<h4>🚨 ROOT CAUSE FOUND!</h4>";
    echo "<p>The _wp_attached_file meta field contains a full file system path instead of a relative path.</p>";
    echo "<p><strong>Current value:</strong> {$attached_file}</p>";
    echo "<p><strong>Should be:</strong> " . str_replace($upload_dir['basedir'] . '/', '', $attached_file) . "</p>";
    echo "</div>";
    
    // Offer to fix it
    echo "<h4>🔧 Fix Option</h4>";
    echo "<p>We can fix this by updating the _wp_attached_file meta to use relative paths.</p>";
    
    if (isset($_GET['fix_metadata']) && $_GET['fix_metadata'] === 'yes') {
        $relative_path = str_replace($upload_dir['basedir'] . '/', '', $attached_file);
        $relative_path = str_replace($upload_dir['basedir'] . '\\', '', $relative_path);
        $relative_path = str_replace('\\', '/', $relative_path);
        
        update_post_meta($attachment_id, '_wp_attached_file', $relative_path);
        echo "<p class='success'>✅ Fixed! Updated _wp_attached_file to: {$relative_path}</p>";
        echo "<p><strong>Please refresh this page to see the results.</strong></p>";
    } else {
        echo "<p><a href='?fix_metadata=yes' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🔧 Fix This Image's Metadata</a></p>";
    }
}

// 8. Check for bulk metadata issues
echo "<h3>8. Bulk Metadata Check</h3>";

global $wpdb;
$problematic_attachments = $wpdb->get_results("
    SELECT post_id, meta_value 
    FROM {$wpdb->postmeta} 
    WHERE meta_key = '_wp_attached_file' 
    AND (meta_value LIKE 'D:%' OR meta_value LIKE 'C:%' OR meta_value LIKE '/%')
    LIMIT 10
");

if ($problematic_attachments) {
    echo "<p class='error'>Found " . count($problematic_attachments) . " attachments with full path metadata:</p>";
    echo "<div class='code'>";
    foreach ($problematic_attachments as $attachment) {
        echo "ID {$attachment->post_id}: {$attachment->meta_value}\n";
    }
    echo "</div>";
    
    if (isset($_GET['fix_all_metadata']) && $_GET['fix_all_metadata'] === 'yes') {
        $fixed_count = 0;
        foreach ($problematic_attachments as $attachment) {
            $relative_path = str_replace($upload_dir['basedir'] . '/', '', $attachment->meta_value);
            $relative_path = str_replace($upload_dir['basedir'] . '\\', '', $relative_path);
            $relative_path = str_replace('\\', '/', $relative_path);
            
            update_post_meta($attachment->post_id, '_wp_attached_file', $relative_path);
            $fixed_count++;
        }
        echo "<p class='success'>✅ Fixed {$fixed_count} attachments!</p>";
    } else {
        echo "<p><a href='?fix_all_metadata=yes' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🔧 Fix All Problematic Metadata</a></p>";
    }
} else {
    echo "<p class='success'>✅ No problematic metadata found in other attachments</p>";
}

echo "<h2>🎯 Summary</h2>";
echo "<p>This analysis should reveal exactly why WordPress is serving local file paths instead of URLs.</p>";
echo "<p>The most common cause is corrupted _wp_attached_file metadata that contains full paths instead of relative paths.</p>";
