<?php
/**
 * Debug Media Library WebP Display Issues
 * 
 * This script helps diagnose why WebP images aren't displaying in Media Library
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only run in admin for administrators
if (!is_admin() || !current_user_can('manage_options')) {
    return;
}

echo "<h2>🔍 Media Library WebP Debug Report</h2>";

// 1. Check if we have any converted images
global $wpdb;

echo "<h3>1. Database Check</h3>";

$converted_images = $wpdb->get_results("
    SELECT p.ID, p.post_title, pm.meta_value
    FROM {$wpdb->posts} p
    INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
    WHERE p.post_type = 'attachment'
    AND pm.meta_key = '_webp_conversion_data'
    AND pm.meta_value LIKE '%\"converted\":true%'
    LIMIT 5
");

echo "<p>Found " . count($converted_images) . " converted images in database</p>";

if (empty($converted_images)) {
    echo "<p style='color: red;'>❌ No converted images found! This explains why Media Library shows broken images.</p>";
    echo "<p>You need to convert some images first before testing Media Library display.</p>";
    return;
}

// 2. Test URL filtering for each converted image
echo "<h3>2. URL Filtering Test</h3>";

foreach ($converted_images as $image) {
    $attachment_id = $image->ID;
    $conversion_data = maybe_unserialize($image->meta_value);
    
    echo "<h4>Testing Attachment ID: {$attachment_id} - {$image->post_title}</h4>";
    
    // Check conversion data structure
    echo "<p><strong>Conversion Data:</strong></p>";
    echo "<pre>" . print_r($conversion_data, true) . "</pre>";
    
    // Test original URL vs filtered URL
    echo "<p><strong>URL Tests:</strong></p>";
    
    // Remove our filters temporarily to get original URL
    remove_filter('wp_get_attachment_url', array($GLOBALS['redco_webp_converter'], 'serve_webp_url_in_media_library'));
    $original_url = wp_get_attachment_url($attachment_id);
    add_filter('wp_get_attachment_url', array($GLOBALS['redco_webp_converter'], 'serve_webp_url_in_media_library'), 10, 2);
    
    // Get filtered URL
    $filtered_url = wp_get_attachment_url($attachment_id);
    
    echo "<p>Original URL: " . $original_url . "</p>";
    echo "<p>Filtered URL: " . $filtered_url . "</p>";
    
    if ($original_url === $filtered_url) {
        echo "<p style='color: red;'>❌ URL filtering is NOT working - URLs are identical</p>";
    } else {
        echo "<p style='color: green;'>✅ URL filtering is working - URLs are different</p>";
    }
    
    // Check if WebP file actually exists
    if (isset($conversion_data['webp_path'])) {
        $webp_path = $conversion_data['webp_path'];
        if (file_exists($webp_path)) {
            echo "<p style='color: green;'>✅ WebP file exists: " . basename($webp_path) . "</p>";
        } else {
            echo "<p style='color: red;'>❌ WebP file missing: " . basename($webp_path) . "</p>";
        }
    }
    
    // Test wp_prepare_attachment_for_js
    $attachment = get_post($attachment_id);
    $js_data = wp_prepare_attachment_for_js($attachment);
    
    if ($js_data && isset($js_data['url'])) {
        echo "<p>JavaScript URL: " . $js_data['url'] . "</p>";
        
        if (strpos($js_data['url'], '.webp') !== false) {
            echo "<p style='color: green;'>✅ JavaScript data contains WebP URL</p>";
        } else {
            echo "<p style='color: red;'>❌ JavaScript data still contains original URL</p>";
        }
    }
    
    echo "<hr>";
}

// 3. Check hook registration
echo "<h3>3. Hook Registration Check</h3>";

// FIXED: Use correct global variable name
$webp_converter = $GLOBALS['redco_webp_instance'] ?? null;

if (!$webp_converter) {
    echo "<p style='color: red;'>❌ WebP converter instance not found in globals</p>";
} else {
    echo "<p style='color: green;'>✅ WebP converter instance found</p>";
    
    // Check specific hooks
    $hooks_to_check = array(
        'wp_get_attachment_url' => 'serve_webp_url_in_media_library',
        'wp_get_attachment_thumb_url' => 'serve_webp_url_in_media_library',
        'wp_prepare_attachment_for_js' => 'fix_media_library_webp_urls'
    );
    
    foreach ($hooks_to_check as $hook => $method) {
        $registered = has_filter($hook, array($webp_converter, $method));
        
        if ($registered !== false) {
            echo "<p style='color: green;'>✅ {$hook} hook is registered (priority: {$registered})</p>";
        } else {
            echo "<p style='color: red;'>❌ {$hook} hook is NOT registered</p>";
        }
    }
}

// 4. Test admin context
echo "<h3>4. Admin Context Check</h3>";

if (is_admin()) {
    echo "<p style='color: green;'>✅ Running in admin context</p>";
} else {
    echo "<p style='color: red;'>❌ NOT running in admin context</p>";
}

// 5. Test method existence
echo "<h3>5. Method Existence Check</h3>";

if ($webp_converter) {
    $methods_to_check = array(
        'serve_webp_url_in_media_library',
        'fix_media_library_webp_urls'
    );
    
    foreach ($methods_to_check as $method) {
        if (method_exists($webp_converter, $method)) {
            echo "<p style='color: green;'>✅ Method {$method} exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Method {$method} does NOT exist</p>";
        }
    }
}

// 6. Manual URL filtering test
echo "<h3>6. Manual URL Filtering Test</h3>";

if (!empty($converted_images) && $webp_converter) {
    $test_attachment_id = $converted_images[0]->ID;
    
    echo "<p>Testing manual URL filtering for attachment {$test_attachment_id}:</p>";
    
    // Get original URL without filters
    $original_url = get_post_meta($test_attachment_id, '_wp_attached_file', true);
    if ($original_url) {
        $upload_dir = wp_upload_dir();
        $full_original_url = $upload_dir['baseurl'] . '/' . $original_url;
        echo "<p>WordPress core URL: {$full_original_url}</p>";
    }
    
    // Test our method directly
    if (method_exists($webp_converter, 'serve_webp_url_in_media_library')) {
        $test_url = "http://example.com/test.jpg";
        $filtered_test_url = $webp_converter->serve_webp_url_in_media_library($test_url, $test_attachment_id);
        
        echo "<p>Direct method test:</p>";
        echo "<p>Input: {$test_url}</p>";
        echo "<p>Output: {$filtered_test_url}</p>";
        
        if ($test_url !== $filtered_test_url) {
            echo "<p style='color: green;'>✅ Method is working - URL was modified</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Method returned original URL (might be expected behavior)</p>";
        }
    }
}

echo "<h3>7. Recommendations</h3>";

if (empty($converted_images)) {
    echo "<p>🔧 <strong>Action Required:</strong> Convert some images to WebP first using the bulk conversion tool.</p>";
} else {
    echo "<p>🔧 <strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>Check if WebP files actually exist on the server</li>";
    echo "<li>Verify hook registration is working correctly</li>";
    echo "<li>Test URL filtering in Media Library interface</li>";
    echo "<li>Check browser console for JavaScript errors</li>";
    echo "</ul>";
}
