<?php
/**
 * CDN Integration Module Settings
 *
 * @package Redco_Optimizer
 * @subpackage CDN_Integration
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance and settings
$is_enabled = redco_is_module_enabled('cdn-integration');
$defaults = Redco_Config::get_module_defaults('cdn-integration');
$current_settings = redco_get_module_option('cdn-integration', 'settings', $defaults);
$provider = isset($current_settings['provider']) ? $current_settings['provider'] : '';

// Get CDN statistics if module is enabled
$cdn_stats = array(
    'provider' => $provider,
    'enabled' => isset($current_settings['enabled']) ? $current_settings['enabled'] : false,
    'total_requests' => get_option('redco_cdn_requests', 0),
    'cache_hits' => get_option('redco_cdn_cache_hits', 0),
    'bandwidth_saved' => get_option('redco_cdn_bandwidth_saved', 0)
);

if ($is_enabled && class_exists('Redco_CDN_Integration')) {
    $cdn_instance = new Redco_CDN_Integration();
    $cdn_stats = $cdn_instance->get_cdn_stats();
}
?>

<!-- Enqueue standardized module CSS -->
<link rel="stylesheet" href="<?php echo REDCO_OPTIMIZER_PLUGIN_URL; ?>assets/css/module-layout-standard.css">

<style>
/* Provider-specific settings styling */
#provider-specific-settings {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.provider-settings-section {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 0;
}

.provider-section-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.provider-section-title .dashicons {
    color: #3b82f6;
    font-size: 18px;
}

.provider-section-description {
    margin: 0 0 20px 0;
    color: #64748b;
    font-size: 14px;
    line-height: 1.5;
}

.cdn-provider-settings {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Enhanced setting items within provider sections */
.provider-settings-section .setting-item {
    margin-bottom: 20px;
}

.provider-settings-section .setting-item:last-child {
    margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .provider-settings-section {
        padding: 15px;
    }

    .provider-section-title {
        font-size: 15px;
    }
}
</style>

<div class="redco-module-tab" data-module="cdn-integration">
    <!-- Enhanced Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>">
                    <?php _e('Modules', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('CDN Integration', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-networking"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('CDN Integration', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Integrate with Content Delivery Networks to serve static assets from edge locations worldwide, reducing load times and server bandwidth.', 'redco-optimizer'); ?></p>

                    <!-- Status Indicators -->
                    <div class="header-status">
                        <?php if ($is_enabled && $cdn_stats['enabled']): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'redco-optimizer'); ?>
                            </div>
                            <?php if (!empty($provider)): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-cloud"></span>
                                    <?php echo esc_html(ucfirst(str_replace('_', ' ', $provider))); ?>
                                </div>
                            <?php endif; ?>
                            <?php if ($cdn_stats['total_requests'] > 0): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-chart-line"></span>
                                    <?php _e('Serving Content', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Inactive', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <?php if ($is_enabled && $cdn_stats['enabled']): ?>
                            <button type="button" class="header-action-btn" id="test-cdn-connection-header">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Test Connection', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="purge-cdn-cache-header">
                                <span class="dashicons dashicons-trash"></span>
                                <?php _e('Purge Cache', 'redco-optimizer'); ?>
                            </button>
                        <?php endif; ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="header-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('All Modules', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>

                <!-- CDN Metrics -->
                <?php if ($is_enabled && $cdn_stats['enabled']): ?>
                    <div class="header-metrics">
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo number_format($cdn_stats['total_requests']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Requests', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $cdn_stats['total_requests'] > 0 ? round(($cdn_stats['cache_hits'] / $cdn_stats['total_requests']) * 100, 1) : 0; ?>%
                            </div>
                            <div class="header-metric-label"><?php _e('Hit Rate', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo size_format($cdn_stats['bandwidth_saved']); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Saved', 'redco-optimizer'); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="cdn-integration">
                    <?php wp_nonce_field('redco_cdn_integration_settings', 'redco_cdn_integration_nonce'); ?>
                    <!-- CDN Configuration Section -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-settings"></span>
                                <?php _e('CDN Configuration', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Configure your Content Delivery Network integration to serve static assets from edge locations worldwide, reducing load times and server bandwidth.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Performance Impact:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level high" id="cdn-impact"><?php _e('High Performance', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-cloud"></span>
                                    <?php _e('CDN Provider Settings', 'redco-optimizer'); ?>
                                </h4>

                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="cdn_settings[enabled]" value="1"
                                               <?php checked(isset($current_settings['enabled']) ? $current_settings['enabled'] : 0, 1); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Enable CDN Integration', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Enable CDN integration to serve static assets from your CDN provider for faster global content delivery.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">🌍 Global delivery</span>
                                            <span class="benefit-item">⚡ Faster loading</span>
                                            <span class="benefit-item">📉 Reduced bandwidth</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-item enhanced">
                                    <label for="cdn_provider" class="setting-label">
                                        <strong><?php _e('CDN Provider', 'redco-optimizer'); ?></strong>
                                        <span class="help-tooltip" title="<?php _e('Choose your CDN provider. Each provider has different configuration requirements.', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                    <div class="setting-control">
                                        <select name="cdn_settings[provider]" id="cdn_provider" class="enhanced-select">
                                            <option value=""><?php _e('Select CDN Provider', 'redco-optimizer'); ?></option>
                                            <option value="cloudflare" <?php selected($provider, 'cloudflare'); ?>>Cloudflare</option>
                                            <option value="bunnycdn" <?php selected($provider, 'bunnycdn'); ?>>BunnyCDN</option>
                                            <option value="keycdn" <?php selected($provider, 'keycdn'); ?>>KeyCDN</option>
                                            <option value="amazon_cloudfront" <?php selected($provider, 'amazon_cloudfront'); ?>>Amazon CloudFront</option>
                                            <option value="sucuri" <?php selected($provider, 'sucuri'); ?>>Sucuri</option>
                                            <option value="fastly" <?php selected($provider, 'fastly'); ?>>Fastly</option>
                                            <option value="custom" <?php selected($provider, 'custom'); ?>>Custom CDN</option>
                                        </select>
                                    </div>
                                    <div class="setting-description">
                                        <p><?php _e('Choose your CDN provider. Each provider offers different features and pricing models.', 'redco-optimizer'); ?></p>
                                    </div>
                                </div>

                                <!-- Provider-Specific Settings (Dynamic) -->
                                <div id="provider-specific-settings">

                                    <!-- Cloudflare Settings -->
                                    <div class="cdn-provider-settings" id="cloudflare-settings" style="<?php echo $provider === 'cloudflare' ? '' : 'display:none;'; ?>">
                                        <div class="provider-settings-section">
                                            <h5 class="provider-section-title">
                                                <span class="dashicons dashicons-cloud"></span>
                                                <?php _e('Cloudflare Configuration', 'redco-optimizer'); ?>
                                            </h5>
                                            <p class="provider-section-description">
                                                <?php _e('Configure your Cloudflare CDN settings. You can find these values in your Cloudflare dashboard.', 'redco-optimizer'); ?>
                                            </p>

                                            <div class="setting-item enhanced">
                                                <label for="cloudflare_zone_id" class="setting-label">
                                                    <strong><?php _e('Zone ID', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Found in the right sidebar of your domain overview page in Cloudflare', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="text" name="cdn_settings[cloudflare_zone_id]" id="cloudflare_zone_id"
                                                           value="<?php echo esc_attr(isset($current_settings['cloudflare_zone_id']) ? $current_settings['cloudflare_zone_id'] : ''); ?>"
                                                           class="enhanced-input" placeholder="e.g., 1234567890abcdef1234567890abcdef">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your Cloudflare Zone ID. Found in the right sidebar of your domain overview page.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>

                                            <div class="setting-item enhanced">
                                                <label for="cloudflare_api_token" class="setting-label">
                                                    <strong><?php _e('API Token', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Create an API token with Zone:Cache Purge permissions', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="password" name="cdn_settings[cloudflare_api_token]" id="cloudflare_api_token"
                                                           value="<?php echo esc_attr(isset($current_settings['cloudflare_api_token']) ? $current_settings['cloudflare_api_token'] : ''); ?>"
                                                           class="enhanced-input" placeholder="Your Cloudflare API Token">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Create an API token with Zone:Cache Purge permissions in your Cloudflare profile.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>

                                            <div class="setting-item enhanced">
                                                <label for="cloudflare_zone_url" class="setting-label">
                                                    <strong><?php _e('Zone URL', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your domain URL as configured in Cloudflare', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="url" name="cdn_settings[cloudflare_zone_url]" id="cloudflare_zone_url"
                                                           value="<?php echo esc_attr(isset($current_settings['cloudflare_zone_url']) ? $current_settings['cloudflare_zone_url'] : ''); ?>"
                                                           class="enhanced-input" placeholder="https://your-domain.com">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your domain URL as configured in Cloudflare. This should match your website URL.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- BunnyCDN Settings -->
                                    <div class="cdn-provider-settings" id="bunnycdn-settings" style="<?php echo $provider === 'bunnycdn' ? '' : 'display:none;'; ?>">
                                        <div class="provider-settings-section">
                                            <h5 class="provider-section-title">
                                                <span class="dashicons dashicons-cloud"></span>
                                                <?php _e('BunnyCDN Configuration', 'redco-optimizer'); ?>
                                            </h5>
                                            <p class="provider-section-description">
                                                <?php _e('Configure your BunnyCDN settings. You can find these values in your BunnyCDN dashboard.', 'redco-optimizer'); ?>
                                            </p>

                                            <div class="setting-item enhanced">
                                                <label for="bunnycdn_api_key" class="setting-label">
                                                    <strong><?php _e('API Key', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your BunnyCDN API key from the account settings', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="password" name="cdn_settings[bunnycdn_api_key]" id="bunnycdn_api_key"
                                                           value="<?php echo esc_attr(isset($current_settings['bunnycdn_api_key']) ? $current_settings['bunnycdn_api_key'] : ''); ?>"
                                                           class="enhanced-input" placeholder="Your BunnyCDN API Key">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your BunnyCDN API key from the account settings.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>

                                            <div class="setting-item enhanced">
                                                <label for="bunnycdn_pull_zone_id" class="setting-label">
                                                    <strong><?php _e('Pull Zone ID', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your BunnyCDN Pull Zone ID', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="text" name="cdn_settings[bunnycdn_pull_zone_id]" id="bunnycdn_pull_zone_id"
                                                           value="<?php echo esc_attr(isset($current_settings['bunnycdn_pull_zone_id']) ? $current_settings['bunnycdn_pull_zone_id'] : ''); ?>"
                                                           class="enhanced-input" placeholder="123456">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your BunnyCDN Pull Zone ID.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>

                                            <div class="setting-item enhanced">
                                                <label for="bunnycdn_pull_zone_url" class="setting-label">
                                                    <strong><?php _e('Pull Zone URL', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your BunnyCDN Pull Zone URL', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="url" name="cdn_settings[bunnycdn_pull_zone_url]" id="bunnycdn_pull_zone_url"
                                                           value="<?php echo esc_attr(isset($current_settings['bunnycdn_pull_zone_url']) ? $current_settings['bunnycdn_pull_zone_url'] : ''); ?>"
                                                           class="enhanced-input" placeholder="https://your-zone.b-cdn.net">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your BunnyCDN Pull Zone URL.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- KeyCDN Settings -->
                                    <div class="cdn-provider-settings" id="keycdn-settings" style="<?php echo $provider === 'keycdn' ? '' : 'display:none;'; ?>">
                                        <div class="provider-settings-section">
                                            <h5 class="provider-section-title">
                                                <span class="dashicons dashicons-cloud"></span>
                                                <?php _e('KeyCDN Configuration', 'redco-optimizer'); ?>
                                            </h5>
                                            <p class="provider-section-description">
                                                <?php _e('Configure your KeyCDN settings. You can find these values in your KeyCDN dashboard.', 'redco-optimizer'); ?>
                                            </p>

                                            <div class="setting-item enhanced">
                                                <label for="keycdn_api_key" class="setting-label">
                                                    <strong><?php _e('API Key', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your KeyCDN API key from the account settings', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="password" name="cdn_settings[keycdn_api_key]" id="keycdn_api_key"
                                                           value="<?php echo esc_attr(isset($current_settings['keycdn_api_key']) ? $current_settings['keycdn_api_key'] : ''); ?>"
                                                           class="enhanced-input" placeholder="Your KeyCDN API Key">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your KeyCDN API key from the account settings.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>

                                            <div class="setting-item enhanced">
                                                <label for="keycdn_zone_id" class="setting-label">
                                                    <strong><?php _e('Zone ID', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your KeyCDN Zone ID', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="text" name="cdn_settings[keycdn_zone_id]" id="keycdn_zone_id"
                                                           value="<?php echo esc_attr(isset($current_settings['keycdn_zone_id']) ? $current_settings['keycdn_zone_id'] : ''); ?>"
                                                           class="enhanced-input" placeholder="123456">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your KeyCDN Zone ID.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>

                                            <div class="setting-item enhanced">
                                                <label for="keycdn_zone_url" class="setting-label">
                                                    <strong><?php _e('Zone URL', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your KeyCDN Zone URL', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="url" name="cdn_settings[keycdn_zone_url]" id="keycdn_zone_url"
                                                           value="<?php echo esc_attr(isset($current_settings['keycdn_zone_url']) ? $current_settings['keycdn_zone_url'] : ''); ?>"
                                                           class="enhanced-input" placeholder="https://your-zone.kxcdn.com">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your KeyCDN Zone URL.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Amazon CloudFront Settings -->
                                    <div class="cdn-provider-settings" id="amazon_cloudfront-settings" style="<?php echo $provider === 'amazon_cloudfront' ? '' : 'display:none;'; ?>">
                                        <div class="provider-settings-section">
                                            <h5 class="provider-section-title">
                                                <span class="dashicons dashicons-cloud"></span>
                                                <?php _e('Amazon CloudFront Configuration', 'redco-optimizer'); ?>
                                            </h5>
                                            <p class="provider-section-description">
                                                <?php _e('Configure your Amazon CloudFront CDN settings. You can find these values in your AWS console.', 'redco-optimizer'); ?>
                                            </p>
                                            <div class="setting-note">
                                                <span class="dashicons dashicons-info"></span>
                                                <?php _e('Note: Full CloudFront integration requires AWS SDK. Basic configuration is available.', 'redco-optimizer'); ?>
                                            </div>

                                            <div class="setting-item enhanced">
                                                <label for="cloudfront_distribution_id" class="setting-label">
                                                    <strong><?php _e('Distribution ID', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your CloudFront Distribution ID from AWS console', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="text" name="cdn_settings[cloudfront_distribution_id]" id="cloudfront_distribution_id"
                                                           value="<?php echo esc_attr(isset($current_settings['cloudfront_distribution_id']) ? $current_settings['cloudfront_distribution_id'] : ''); ?>"
                                                           class="enhanced-input" placeholder="E1234567890123">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your CloudFront Distribution ID from the AWS console.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>

                                            <div class="setting-item enhanced">
                                                <label for="cloudfront_distribution_url" class="setting-label">
                                                    <strong><?php _e('Distribution URL', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your CloudFront Distribution URL', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="url" name="cdn_settings[cloudfront_distribution_url]" id="cloudfront_distribution_url"
                                                           value="<?php echo esc_attr(isset($current_settings['cloudfront_distribution_url']) ? $current_settings['cloudfront_distribution_url'] : ''); ?>"
                                                           class="enhanced-input" placeholder="https://d1234567890123.cloudfront.net">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your CloudFront Distribution URL.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Sucuri Settings -->
                                    <div class="cdn-provider-settings" id="sucuri-settings" style="<?php echo $provider === 'sucuri' ? '' : 'display:none;'; ?>">
                                        <div class="provider-settings-section">
                                            <h5 class="provider-section-title">
                                                <span class="dashicons dashicons-cloud"></span>
                                                <?php _e('Sucuri Configuration', 'redco-optimizer'); ?>
                                            </h5>
                                            <p class="provider-section-description">
                                                <?php _e('Configure your Sucuri CDN settings. You can find these values in your Sucuri dashboard.', 'redco-optimizer'); ?>
                                            </p>

                                            <div class="setting-item enhanced">
                                                <label for="sucuri_api_key" class="setting-label">
                                                    <strong><?php _e('API Key', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your Sucuri API key', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="password" name="cdn_settings[sucuri_api_key]" id="sucuri_api_key"
                                                           value="<?php echo esc_attr(isset($current_settings['sucuri_api_key']) ? $current_settings['sucuri_api_key'] : ''); ?>"
                                                           class="enhanced-input" placeholder="Your Sucuri API Key">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your Sucuri API key from the account settings.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>

                                            <div class="setting-item enhanced">
                                                <label for="sucuri_api_secret" class="setting-label">
                                                    <strong><?php _e('API Secret', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your Sucuri API secret', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="password" name="cdn_settings[sucuri_api_secret]" id="sucuri_api_secret"
                                                           value="<?php echo esc_attr(isset($current_settings['sucuri_api_secret']) ? $current_settings['sucuri_api_secret'] : ''); ?>"
                                                           class="enhanced-input" placeholder="Your Sucuri API Secret">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your Sucuri API secret from the account settings.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>

                                            <div class="setting-item enhanced">
                                                <label for="sucuri_cdn_url" class="setting-label">
                                                    <strong><?php _e('CDN URL', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your Sucuri CDN URL', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="url" name="cdn_settings[sucuri_cdn_url]" id="sucuri_cdn_url"
                                                           value="<?php echo esc_attr(isset($current_settings['sucuri_cdn_url']) ? $current_settings['sucuri_cdn_url'] : ''); ?>"
                                                           class="enhanced-input" placeholder="https://your-domain.sucuri.net">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your Sucuri CDN URL.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Fastly Settings -->
                                    <div class="cdn-provider-settings" id="fastly-settings" style="<?php echo $provider === 'fastly' ? '' : 'display:none;'; ?>">
                                        <div class="provider-settings-section">
                                            <h5 class="provider-section-title">
                                                <span class="dashicons dashicons-cloud"></span>
                                                <?php _e('Fastly Configuration', 'redco-optimizer'); ?>
                                            </h5>
                                            <p class="provider-section-description">
                                                <?php _e('Configure your Fastly CDN settings. You can find these values in your Fastly dashboard.', 'redco-optimizer'); ?>
                                            </p>

                                            <div class="setting-item enhanced">
                                                <label for="fastly_api_token" class="setting-label">
                                                    <strong><?php _e('API Token', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your Fastly API token', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="password" name="cdn_settings[fastly_api_token]" id="fastly_api_token"
                                                           value="<?php echo esc_attr(isset($current_settings['fastly_api_token']) ? $current_settings['fastly_api_token'] : ''); ?>"
                                                           class="enhanced-input" placeholder="Your Fastly API Token">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your Fastly API token from the account settings.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>

                                            <div class="setting-item enhanced">
                                                <label for="fastly_service_id" class="setting-label">
                                                    <strong><?php _e('Service ID', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your Fastly Service ID', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="text" name="cdn_settings[fastly_service_id]" id="fastly_service_id"
                                                           value="<?php echo esc_attr(isset($current_settings['fastly_service_id']) ? $current_settings['fastly_service_id'] : ''); ?>"
                                                           class="enhanced-input" placeholder="1234567890abcdef">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your Fastly Service ID.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>

                                            <div class="setting-item enhanced">
                                                <label for="fastly_service_url" class="setting-label">
                                                    <strong><?php _e('Service URL', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your Fastly Service URL', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="url" name="cdn_settings[fastly_service_url]" id="fastly_service_url"
                                                           value="<?php echo esc_attr(isset($current_settings['fastly_service_url']) ? $current_settings['fastly_service_url'] : ''); ?>"
                                                           class="enhanced-input" placeholder="https://your-service.fastly.com">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your Fastly Service URL.', 'redco-optimizer'); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Custom CDN Settings -->
                                    <div class="cdn-provider-settings" id="custom-settings" style="<?php echo $provider === 'custom' ? '' : 'display:none;'; ?>">
                                        <div class="provider-settings-section">
                                            <h5 class="provider-section-title">
                                                <span class="dashicons dashicons-cloud"></span>
                                                <?php _e('Custom CDN Configuration', 'redco-optimizer'); ?>
                                            </h5>
                                            <p class="provider-section-description">
                                                <?php _e('Configure your custom CDN settings. This option allows you to use any CDN provider by specifying the CDN URL.', 'redco-optimizer'); ?>
                                            </p>

                                            <div class="setting-item enhanced">
                                                <label for="custom_cdn_url" class="setting-label">
                                                    <strong><?php _e('CDN URL', 'redco-optimizer'); ?></strong>
                                                    <span class="help-tooltip" title="<?php _e('Your custom CDN URL', 'redco-optimizer'); ?>">?</span>
                                                </label>
                                                <div class="setting-control">
                                                    <input type="url" name="cdn_settings[custom_cdn_url]" id="custom_cdn_url"
                                                           value="<?php echo esc_attr(isset($current_settings['custom_cdn_url']) ? $current_settings['custom_cdn_url'] : ''); ?>"
                                                           class="enhanced-input" placeholder="https://cdn.your-domain.com">
                                                </div>
                                                <div class="setting-description">
                                                    <p><?php _e('Your custom CDN URL. This will replace your site URL for static assets.', 'redco-optimizer'); ?></p>
                                                    <div class="setting-note">
                                                        <span class="dashicons dashicons-info"></span>
                                                        <?php _e('Note: Cache purging and advanced features may not be available with custom CDN configurations.', 'redco-optimizer'); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h4 class="section-title">
                                    <span class="dashicons dashicons-media-document"></span>
                                    <?php _e('File Types Configuration', 'redco-optimizer'); ?>
                                </h4>

                                <div class="setting-item enhanced">
                                    <label class="setting-label">
                                        <strong><?php _e('File Types to Serve via CDN', 'redco-optimizer'); ?></strong>
                                        <span class="help-tooltip" title="<?php _e('Select which file types should be served through your CDN', 'redco-optimizer'); ?>">?</span>
                                    </label>
                                    <div class="setting-control">
                                        <div class="checkbox-list">
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_images]" value="1"
                                                       <?php checked(isset($current_settings['enable_images']) ? $current_settings['enable_images'] : 1, 1); ?>
                                                       id="enable_images" class="enhanced-checkbox">
                                                <label for="enable_images"><?php _e('Images (JPG, PNG, GIF, WebP, SVG)', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_css]" value="1"
                                                       <?php checked(isset($current_settings['enable_css']) ? $current_settings['enable_css'] : 1, 1); ?>
                                                       id="enable_css" class="enhanced-checkbox">
                                                <label for="enable_css"><?php _e('CSS Files', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_js]" value="1"
                                                       <?php checked(isset($current_settings['enable_js']) ? $current_settings['enable_js'] : 1, 1); ?>
                                                       id="enable_js" class="enhanced-checkbox">
                                                <label for="enable_js"><?php _e('JavaScript Files', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_fonts]" value="1"
                                                       <?php checked(isset($current_settings['enable_fonts']) ? $current_settings['enable_fonts'] : 1, 1); ?>
                                                       id="enable_fonts" class="enhanced-checkbox">
                                                <label for="enable_fonts"><?php _e('Font Files (WOFF, WOFF2, TTF)', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_media]" value="1"
                                                       <?php checked(isset($current_settings['enable_media']) ? $current_settings['enable_media'] : 0, 1); ?>
                                                       id="enable_media" class="enhanced-checkbox">
                                                <label for="enable_media"><?php _e('Media Files (MP4, MP3, etc.)', 'redco-optimizer'); ?></label>
                                            </div>
                                            <div class="checkbox-item">
                                                <input type="checkbox" name="cdn_settings[enable_documents]" value="1"
                                                       <?php checked(isset($current_settings['enable_documents']) ? $current_settings['enable_documents'] : 0, 1); ?>
                                                       id="enable_documents" class="enhanced-checkbox">
                                                <label for="enable_documents"><?php _e('Documents (PDF, ZIP, etc.)', 'redco-optimizer'); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="setting-description">
                                        <p><?php _e('Select which file types should be served through your CDN. Images and CSS/JS files typically provide the best performance improvements.', 'redco-optimizer'); ?></p>
                                        <div class="setting-note">
                                            <span class="dashicons dashicons-info"></span>
                                            <?php _e('Tip: Start with images and CSS/JS files for immediate performance benefits.', 'redco-optimizer'); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>







                    <!-- Advanced Options -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Advanced CDN Options', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Advanced configuration options for fine-tuning your CDN integration behavior.', 'redco-optimizer'); ?>
                                </p>
                            </div>

                            <div class="settings-section">
                                <div class="setting-item enhanced">
                                    <label class="setting-label checkbox-label">
                                        <input type="checkbox" name="cdn_settings[skip_logged_in]" value="1"
                                               <?php checked(isset($current_settings['skip_logged_in']) ? $current_settings['skip_logged_in'] : 1, 1); ?> class="enhanced-checkbox">
                                        <strong><?php _e('Skip CDN for Logged-in Users', 'redco-optimizer'); ?></strong>
                                        <span class="recommended-badge"><?php _e('Recommended', 'redco-optimizer'); ?></span>
                                    </label>
                                    <div class="setting-description">
                                        <p><?php _e('Disable CDN for logged-in users to avoid caching issues with dynamic content and admin functionality.', 'redco-optimizer'); ?></p>
                                        <div class="setting-benefits">
                                            <span class="benefit-item">🔒 Prevents admin issues</span>
                                            <span class="benefit-item">🎯 Dynamic content support</span>
                                            <span class="benefit-item">✅ Better compatibility</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CDN Management Actions -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-performance"></span>
                                <?php _e('CDN Management', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('Test your CDN connection and manage cache to ensure optimal performance.', 'redco-optimizer'); ?>
                                </p>
                            </div>

                            <div class="settings-section">
                                <div class="action-buttons-grid">
                                    <div class="action-button-item">
                                        <button type="button" id="test-cdn-connection" class="redco-button redco-button-secondary">
                                            <span class="dashicons dashicons-admin-tools"></span>
                                            <?php _e('Test CDN Connection', 'redco-optimizer'); ?>
                                        </button>
                                        <p class="action-description"><?php _e('Verify that your CDN is properly configured and responding.', 'redco-optimizer'); ?></p>
                                    </div>

                                    <div class="action-button-item">
                                        <button type="button" id="purge-cdn-cache" class="redco-button redco-button-secondary">
                                            <span class="dashicons dashicons-trash"></span>
                                            <?php _e('Purge CDN Cache', 'redco-optimizer'); ?>
                                        </button>
                                        <p class="action-description"><?php _e('Clear all cached content from your CDN to force fresh content delivery.', 'redco-optimizer'); ?></p>
                                    </div>
                                </div>

                                <div id="cdn-test-result" class="redco-test-result" style="display:none;"></div>
                                <div id="cdn-purge-result" class="redco-test-result" style="display:none;"></div>
                            </div>
                        </div>
                    </div>

                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- CDN Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('CDN Performance', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-requests">
                                <span class="stat-value"><?php echo number_format($cdn_stats['total_requests']); ?></span>
                                <span class="stat-label"><?php _e('Total Requests', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-hits">
                                <span class="stat-value"><?php echo number_format($cdn_stats['cache_hits']); ?></span>
                                <span class="stat-label"><?php _e('Cache Hits', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-ratio">
                                <span class="stat-value"><?php echo $cdn_stats['total_requests'] > 0 ? round(($cdn_stats['cache_hits'] / $cdn_stats['total_requests']) * 100, 1) : 0; ?>%</span>
                                <span class="stat-label"><?php _e('Hit Ratio', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-bandwidth">
                                <span class="stat-value"><?php echo size_format($cdn_stats['bandwidth_saved']); ?></span>
                                <span class="stat-label"><?php _e('Bandwidth Saved', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CDN Provider Info -->
                <?php if (!empty($provider)): ?>
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-cloud"></span>
                            <?php _e('Provider Info', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="provider-info">
                            <div class="provider-name">
                                <strong><?php echo esc_html(ucfirst(str_replace('_', ' ', $provider))); ?></strong>
                            </div>
                            <div class="provider-status">
                                <?php if ($cdn_stats['enabled']): ?>
                                    <span class="status-badge enabled">
                                        <span class="dashicons dashicons-yes-alt"></span>
                                        <?php _e('Connected', 'redco-optimizer'); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="status-badge disabled">
                                        <span class="dashicons dashicons-warning"></span>
                                        <?php _e('Not Connected', 'redco-optimizer'); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Quick Tips -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-lightbulb"></span>
                            <?php _e('Optimization Tips', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="tips-list">
                            <div class="tip-item">
                                <span class="tip-icon">💡</span>
                                <span class="tip-text"><?php _e('Start with images and CSS/JS files for immediate performance benefits.', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="tip-item">
                                <span class="tip-icon">🌍</span>
                                <span class="tip-text"><?php _e('CDN provides the most benefit for users far from your server location.', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="tip-item">
                                <span class="tip-icon">📊</span>
                                <span class="tip-text"><?php _e('Monitor your CDN hit ratio - aim for 80% or higher for optimal performance.', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="tip-item">
                                <span class="tip-icon">🔄</span>
                                <span class="tip-text"><?php _e('Purge CDN cache after major site updates to ensure fresh content delivery.', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
        <div class="module-disabled-notice">
            <div class="redco-notice warning">
                <p><?php _e('This module is currently disabled. Enable it to access CDN integration settings.', 'redco-optimizer'); ?></p>
            </div>
        </div>
    <?php endif; ?>
</div>



<script>
jQuery(document).ready(function($) {
    // Show/hide provider-specific settings
    $('#cdn_provider').on('change', function() {
        $('.cdn-provider-settings').hide();
        var provider = $(this).val();
        if (provider) {
            $('#' + provider + '-settings').show();
        }
    });

    // Test CDN connection (both header and main buttons)
    $('#test-cdn-connection, #test-cdn-connection-header').on('click', function() {
        var $button = $(this);
        var $result = $('#cdn-test-result');
        var originalText = $button.text();

        $button.prop('disabled', true).text('Testing...');
        $result.hide();

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_test_cdn_connection',
                nonce: '<?php echo wp_create_nonce("redco_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $result.removeClass('error').addClass('success')
                           .html('<div class="notice notice-success"><p><strong>Success:</strong> ' + response.data.message + '</p></div>').show();
                } else {
                    $result.removeClass('success').addClass('error')
                           .html('<div class="notice notice-error"><p><strong>Error:</strong> ' + response.data.message + '</p></div>').show();
                }
            },
            error: function() {
                $result.removeClass('success').addClass('error')
                       .html('<div class="notice notice-error"><p><strong>Error:</strong> Failed to test CDN connection</p></div>').show();
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    });

    // Purge CDN cache (both header and main buttons)
    $('#purge-cdn-cache, #purge-cdn-cache-header').on('click', function() {
        var $button = $(this);
        var $result = $('#cdn-purge-result');
        var originalText = $button.text();

        $button.prop('disabled', true).text('Purging...');
        $result.hide();

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_purge_cdn_cache',
                nonce: '<?php echo wp_create_nonce("redco_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $result.removeClass('error').addClass('success')
                           .html('<div class="notice notice-success"><p><strong>Success:</strong> ' + response.data.message + '</p></div>').show();
                } else {
                    $result.removeClass('success').addClass('error')
                           .html('<div class="notice notice-error"><p><strong>Error:</strong> ' + response.data.message + '</p></div>').show();
                }
            },
            error: function() {
                $result.removeClass('success').addClass('error')
                       .html('<div class="notice notice-error"><p><strong>Error:</strong> Failed to purge CDN cache</p></div>').show();
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    });

    // Performance impact indicator
    $('#cdn_provider').on('change', function() {
        var provider = $(this).val();
        var $impact = $('#cdn-impact');

        if (provider) {
            $impact.removeClass('low medium').addClass('high').text('High Performance');
        } else {
            $impact.removeClass('high medium').addClass('low').text('No Impact');
        }
    });
});
</script>
