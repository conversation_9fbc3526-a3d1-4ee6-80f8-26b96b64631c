/**
 * Enhanced Module Layout CSS - WP Rocket Inspired Design
 * Professional, clean, and modern styling for all RedCo Optimizer modules
 * Redco Optimizer Plugin
 */

/* Global Typography Enhancements */
.redco-module-tab {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.6;
    color: var(--redco-text);
}

.redco-module-tab h1,
.redco-module-tab h2,
.redco-module-tab h3,
.redco-module-tab h4,
.redco-module-tab h5,
.redco-module-tab h6 {
    font-weight: 600;
    line-height: 1.3;
    color: var(--redco-text);
}

.redco-module-tab p {
    line-height: 1.6;
    color: var(--redco-text);
    margin-bottom: 16px;
}

.redco-module-tab small {
    color: var(--redco-text-light);
    font-size: 13px;
}

/* CSS Variables for consistent theming - Original Green Branding with WP Rocket Enhancements */
:root {
    --redco-primary: #4CAF50;
    --redco-primary-dark: #388E3C;
    --redco-primary-light: #66BB6A;
    --redco-secondary: #64748b;
    --redco-success: #16a34a;
    --redco-warning: #f59e0b;
    --redco-danger: #dc2626;
    --redco-info: #2563eb;
    --redco-light: #f8fafc;
    --redco-dark: #1e293b;
    --redco-text: #374151;
    --redco-text-light: #6b7280;
    --redco-border: #e5e7eb;
    --redco-border-light: #f3f4f6;
    --redco-bg-section: #ffffff;
    --redco-bg-card: #ffffff;
    --redco-bg-subtle: #f9fafb;
    --redco-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --redco-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --redco-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --redco-radius: 6px;
    --redco-radius-lg: 8px;

    /* WordPress Admin Bar Height - Standard WordPress CSS Custom Property */
    --wp-admin--admin-bar--height: 32px;
}

/* Enhanced Module Tab Container - WP Rocket Style */
.redco-module-tab {
    background: var(--redco-bg-subtle);
    min-height: 100vh;
    padding: 0px 0px 60px 0px;
    margin: 0;
}

/* Enhanced WordPress Admin Tab Styling */
.redco-optimizer-tabs .nav-tab,
.nav-tab-wrapper .nav-tab {
    font-size: 15px !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
}

.redco-optimizer-tabs .nav-tab-active,
.nav-tab-wrapper .nav-tab-active {
    font-size: 15px !important;
    font-weight: 700 !important;
    line-height: 1.4 !important;
}

/* Enhanced Dashicon Styling for Tabs */
.redco-optimizer-tabs .nav-tab:not(.nav-tab-active) .dashicons,
.nav-tab-wrapper .nav-tab:not(.nav-tab-active) .dashicons {
    color: var(--redco-primary) !important;
}

/* Specific Dashboard Icon Color for Inactive Tabs */
.redco-optimizer-tabs .nav-tab:not(.nav-tab-active) .dashicons-dashboard,
.nav-tab-wrapper .nav-tab:not(.nav-tab-active) .dashicons-dashboard {
    color: var(--redco-primary) !important;
}

/* Enhanced Navigation Title Typography */
.nav-title,
.redco-nav-title,
.navigation-title {
    font-weight: 600 !important;
    font-size: 14px !important;
    line-height: 1.8 !important;
    color: var(--redco-text) !important;
}

/* Ensure tab titles maintain proper spacing with icons */
.redco-optimizer-tabs .nav-tab .dashicons,
.nav-tab-wrapper .nav-tab .dashicons {
    margin-right: 6px !important;
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
    vertical-align: middle !important;
}

/* Enhanced Professional Header Section */
.module-header-section {
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-dark) 100%);
    margin: 0 -20px 30px -2px;
    padding: 0;
    color: white;
    border-radius: 0;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    position: relative;
    overflow: hidden;
    min-height: 182px;
}

/* Header Background Pattern */
.module-header-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

/* Breadcrumb Navigation */
.header-breadcrumb {
    background: rgba(0, 0, 0, 0.1);
    padding: 8px 40px 8px 50px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 12px;
}

.breadcrumb-nav {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.8);
}

.breadcrumb-nav a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-nav a:hover {
    color: white;
    text-decoration: underline;
}

.breadcrumb-separator {
    color: rgba(255, 255, 255, 0.5);
    margin: 0 4px;
}

.breadcrumb-current {
    color: white;
    font-weight: 500;
}

/* Main Header Content */
.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 40px 20px 50px;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 30px;
    align-items: start;
    position: relative;
    z-index: 1;
}

.header-main {
    display: flex;
    align-items: center;
    gap: 20px;
    min-width: 0;
}

.header-icon {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    flex-shrink: 0;
}

.header-icon .dashicons {
    font-size: 36px;
    width: 36px;
    height: 36px;
    color: white;
}

.header-text {
    flex: 1;
    min-width: 0;
}

.header-text h1 {
    color: white;
    margin: 0 0 6px 0;
    font-size: 28px;
    font-weight: 700;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-text p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0 0 12px 0;
    font-size: 14px;
    line-height: 1.4;
    max-width: 500px;
}

/* Module Status Indicators */
.header-status {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
}

.status-badge.enabled {
    background: rgba(76, 175, 80, 0.3);
    border-color: rgba(76, 175, 80, 0.5);
    color: #e8f5e8;
}

.status-badge.disabled {
    background: rgba(239, 68, 68, 0.3);
    border-color: rgba(239, 68, 68, 0.5);
    color: #fecaca;
}

.status-badge.performance {
    background: rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.5);
    color: #dbeafe;
}

.status-badge .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Header Actions */
.header-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: flex-end;
    min-width: 280px;
}

.header-action-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    width: 100%;
}

/* Header Quick Actions */
.header-quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    width: 100%;
}

.header-action-btn {
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    color: white;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    white-space: nowrap;
}

.header-action-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-action-btn:active {
    transform: translateY(0);
}

.header-action-btn .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

.header-action-btn.primary {
    background: rgba(255, 255, 255, 0.9);
    color: var(--redco-primary);
    border-color: rgba(255, 255, 255, 0.9);
}

.header-action-btn.primary:hover {
    background: white;
    color: var(--redco-primary-dark);
    border-color: white;
}

.header-action-btn.optimize {
    background: #ff9800;
    color: white;
    border-color: #ff9800;
}

.header-action-btn.optimize:hover {
    background: #f57c00;
    color: white;
    border-color: #f57c00;
}



/* Performance Metrics in Header */
.header-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    width: 100%;
}

.header-metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
}

.header-metric-value {
    font-size: 16px;
    font-weight: 700;
    color: white;
    line-height: 1;
}

.header-metric-label {
    font-size: 9px;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.3px;
    margin-top: 2px;
}

/* Enhanced Main Layout - WP Rocket Style */
.redco-module-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

.module-layout {
    display: flex;
    gap: 36px;
    align-items: stretch;
    min-height: calc(100vh - 300px);
}

.redco-content-main {
    flex: 1;
    min-width: 0;
}

.redco-content-sidebar {
    width: 280px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
}

/* Enhanced Card Styling - WP Rocket Inspired */
.redco-card {
    background: var(--redco-bg-card);
    border-radius: var(--redco-radius);
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-bottom: 32px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.redco-card:hover {
    box-shadow: var(--redco-shadow-md);
    border-color: #d1d5db;
}

.card-header {
    background: var(--redco-bg-card);
    border-bottom: 1px solid var(--redco-border-light);
    padding: 24px 28px 20px 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.card-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--redco-text);
    display: flex;
    align-items: center;
    gap: 12px;
    line-height: 1.3;
}

.card-header h3 .dashicons {
    color: var(--redco-primary);
    font-size: 22px;
    width: 22px;
    height: 22px;
    margin-top: -2px;
}

.card-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.card-content {
    padding: 28px;
    background: var(--redco-bg-card);
}

/* Enhanced Sidebar Header */
.redco-sidebar-header {
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-dark) 100%);
    color: #fff;
    padding: 20px 20px 20px 20px;
    margin: 0;
    border-radius: 0 0 0 16px;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex-shrink: 0;
    min-height: 178px;
}

/* Enhanced Sidebar Sections - WP Rocket Style */
.redco-sidebar-section {
    background: var(--redco-bg-section);
    border-radius: var(--redco-radius);
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-bottom: 24px;
    overflow: hidden;
}

.sidebar-section-header {
    background: var(--redco-bg-section);
    border-bottom: 1px solid var(--redco-border-light);
    padding: 16px 20px 14px 20px;
}

.sidebar-section-header h3 {
    margin: 0;
    font-size: 15px;
    font-weight: 600;
    color: var(--redco-text);
    display: flex;
    align-items: center;
    gap: 8px;
    line-height: 1.3;
}

.sidebar-section-header h3 .dashicons {
    color: var(--redco-primary);
    font-size: 18px;
    width: 18px;
    height: 18px;
}

.sidebar-section-content {
    padding: 16px 20px;
}

.sidebar-section-content .description {
    font-size: 13px;
    color: var(--redco-text-light);
    margin-top: 8px;
    margin-bottom: 0;
    line-height: 1.5;
}

/* Statistics Display */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

/* REMOVED: Duplicate stat definitions - using standardized colors below */

/* Enhanced Form Elements - WP Rocket Inspired */
.form-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 24px;
}

.form-table th {
    width: 240px;
    padding: 24px 20px 24px 0;
    text-align: left;
    font-weight: 600;
    color: var(--redco-text);
    vertical-align: top;
    font-size: 15px;
    line-height: 1.4;
}

.form-table td {
    padding: 24px 0;
    vertical-align: top;
    line-height: 1.6;
}

.form-table tr {
    border-bottom: 1px solid var(--redco-border-light);
    transition: background-color 0.2s ease;
}

.form-table tr:last-child {
    border-bottom: none;
}

.form-table tr:hover {
    background-color: var(--redco-bg-subtle);
}

/* Enhanced Checkbox Styling - WP Rocket Inspired */
.form-table td label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    font-weight: 500;
    color: var(--redco-text);
    cursor: pointer;
    line-height: 1.5;
    transition: color 0.2s ease;
    font-size: 15px;
}

.form-table td label:hover {
    color: var(--redco-primary);
}

.form-table td input[type="checkbox"] {
    margin: 3px 0 0 0;
    flex-shrink: 0;
    width: 18px;
    height: 18px;
    accent-color: var(--redco-primary);
    border-radius: 3px;
}

.form-table td .description {
    margin-top: 12px;
    color: var(--redco-text-light);
    font-size: 14px;
    line-height: 1.6;
    font-style: normal;
}

/* Enhanced Settings Sections - WP Rocket Inspired */
.redco-settings-section {
    background: var(--redco-bg-section);
    border-radius: var(--redco-radius);
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-bottom: 32px;
    overflow: hidden;
}

.redco-settings-section h3 {
    background: var(--redco-bg-section);
    border-bottom: 1px solid var(--redco-border-light);
    padding: 24px 28px 20px 28px;
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--redco-text);
    display: flex;
    align-items: center;
    gap: 12px;
    line-height: 1.3;
}

.redco-settings-section h3 .dashicons {
    color: var(--redco-primary);
    font-size: 22px;
    width: 22px;
    height: 22px;
    margin-top: -2px;
}

.redco-settings-section .form-table {
    margin: 0;
}

.redco-settings-section .form-table th,
.redco-settings-section .form-table td {
    padding: 24px 28px;
    border-bottom: 1px solid var(--redco-border-light);
}

.redco-settings-section .form-table tr:last-child th,
.redco-settings-section .form-table tr:last-child td {
    border-bottom: none;
}

/* Enhanced Button Styling - WP Rocket Inspired */
.button-primary {
    background: var(--redco-primary) !important;
    border-color: var(--redco-primary) !important;
    color: white !important;
    border-radius: var(--redco-radius) !important;
    padding: 10px 20px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
    box-shadow: var(--redco-shadow) !important;
}

.button-primary:hover {
    background: var(--redco-primary-dark) !important;
    border-color: var(--redco-primary-dark) !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--redco-shadow-md) !important;
}

.button-secondary {
    border-radius: var(--redco-radius) !important;
    padding: 10px 20px !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
    border-color: var(--redco-border) !important;
    color: var(--redco-text) !important;
}

.button-secondary:hover {
    transform: translateY(-1px) !important;
    border-color: var(--redco-primary) !important;
    color: var(--redco-primary) !important;
}

/* Module Disabled State */
.redco-module-disabled {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin: 20px auto;
    max-width: 600px;
}

.disabled-message .dashicons {
    font-size: 48px;
    color: var(--redco-secondary);
    margin-bottom: 20px;
}

.disabled-message h3 {
    font-size: 24px;
    color: var(--redco-dark);
    margin-bottom: 16px;
}

.disabled-message p {
    color: var(--redco-secondary);
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 24px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .module-layout {
        flex-direction: column;
    }

    .redco-content-sidebar {
        width: 100%;
        margin-top: 0;
    }

    .header-content {
        padding: 30px 20px 0 20px;
        max-height: 200px;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        padding: 30px 10px 0 10px;
        max-height: 200px;
    }

    .header-main {
        flex-direction: column;
        text-align: center;
    }

    .header-text h1 {
        font-size: 24px;
    }

    .redco-module-content {
        padding: 0 10px;
    }

    .module-header-section {
        margin: 0 -10px 20px -10px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .card-content {
        padding: 16px;
    }

    .sidebar-section-header {
        padding: 16px 20px;
    }

    .sidebar-section-content {
        padding: 16px;
    }

    .card-header {
        padding: 16px 20px;
    }

    .redco-settings-section h3 {
        padding: 16px 20px;
    }

    .redco-settings-section .form-table th,
    .redco-settings-section .form-table td {
        padding: 16px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Enhanced File Exclusion Lists */
.checkbox-list {
    background: #f8f9fa;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    max-height: 250px;
    overflow-y: auto;
}

.checkbox-list .checkbox-item {
    margin-bottom: 12px;
    padding: 8px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.checkbox-list .checkbox-item:hover {
    border-color: var(--redco-primary);
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
}

.checkbox-list .checkbox-item:last-child {
    margin-bottom: 0;
}

.checkbox-list .checkbox-item label {
    margin: 0;
    font-weight: 500;
    color: var(--redco-dark);
    cursor: pointer;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.checkbox-list .checkbox-item input[type="checkbox"] {
    margin: 2px 0 0 0;
    flex-shrink: 0;
    accent-color: var(--redco-primary);
}

.checkbox-list .checkbox-item small {
    color: var(--redco-secondary);
    font-size: 12px;
    margin-top: 2px;
    display: block;
}

/* Enhanced Statistics Display */
.stat-item {
    text-align: center;
    padding: 20px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--redco-primary);
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--redco-primary), var(--redco-primary-dark));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-item:hover::before {
    opacity: 1;
}

/* REMOVED: Another duplicate stat definition - using standardized colors below */

/* Enhanced Button Styling */
.redco-sidebar-section .button {
    border-radius: 8px !important;
    padding: 12px 16px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    text-decoration: none !important;
}

.redco-sidebar-section .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.redco-sidebar-section .button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Enhanced Card Headers - WP Rocket Style */
.card-header {
    background: var(--redco-bg-card);
    border-bottom: 1px solid var(--redco-border-light);
    padding: 24px 28px 20px 28px;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 28px;
    right: 28px;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--redco-primary), transparent);
    opacity: 0.2;
}

.card-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--redco-text);
    display: flex;
    align-items: center;
    gap: 12px;
    line-height: 1.3;
}

.card-header h3 .dashicons {
    color: var(--redco-primary);
    font-size: 22px;
    width: 22px;
    height: 22px;
    margin-top: -2px;
}

/* Enhanced Cache Statistics Sections - Compact Design */

/* Compact Cache Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 8px;
}

.stat-item {
    text-align: center;
    padding: 8px 6px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.stat-item:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

/* STANDARDIZED STATISTICS COLORS - Used by ALL modules */
.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #475569 !important; /* Consistent neutral slate color for all modules */
    margin-bottom: 2px;
    display: block;
    line-height: 1.2;
}

.stat-label {
    font-size: 10px;
    color: #64748b !important; /* Consistent neutral slate color for all modules */
    text-transform: uppercase;
    letter-spacing: 0.3px;
    font-weight: 500;
    line-height: 1.1;
}

/* SPECIAL CASE: Cache Statistics Colors - Meaningful color coding for cache performance */
.stat-hits .stat-value {
    color: #2e7d32 !important; /* Dark green for successful hits */
    font-weight: 700 !important;
}

.stat-hits .stat-label {
    color: #1b5e20 !important; /* Darker green for label */
}

.stat-hits:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.stat-misses .stat-value {
    color: #e65100 !important; /* Dark orange for misses */
    font-weight: 700 !important;
}

.stat-misses .stat-label {
    color: #bf360c !important; /* Darker orange for label */
}

.stat-misses:hover {
    border-color: #ff9800;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);
}

.stat-size .stat-value {
    color: #1565c0 !important; /* Dark blue for cache size */
    font-weight: 700 !important;
}

.stat-size .stat-label {
    color: #0d47a1 !important; /* Darker blue for label */
}

.stat-size:hover {
    border-color: #2196f3;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

.stat-ratio .stat-value {
    color: #6a1b9a !important; /* Dark purple for hit ratio */
    font-weight: 700 !important;
}

.stat-ratio .stat-label {
    color: #4a148c !important; /* Darker purple for label */
}

.stat-ratio:hover {
    border-color: #9c27b0;
    box-shadow: 0 2px 8px rgba(156, 39, 176, 0.2);
}

/* NOTE: Module-specific color overrides removed - all modules now use standardized colors above */

/* All module-specific color overrides removed - using standardized colors above */

/* Enhanced Database Cleanup Options Styling */

/* Note: Card Header Actions removed - buttons no longer exist in UI */

/* Cleanup Intro Section */
.cleanup-intro {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #e2e8f0;
}

.cleanup-summary {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    font-size: 13px;
}

.total-items {
    color: #64748b;
    font-weight: 600;
}

.estimated-space {
    color: #64748b;
    font-style: italic;
}

/* Cleanup Sections */
.cleanup-section {
    margin-bottom: 24px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
}

.section-title {
    margin: 0;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title .dashicons {
    color: #64748b;
    font-size: 16px;
}

/* Enhanced Cleanup Options */
.cleanup-option {
    padding: 16px;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
    position: relative;
}

.cleanup-option:last-child {
    border-bottom: none;
}

.cleanup-option:hover {
    background: #fafbfc;
}

.cleanup-option.recommended {
    border-left: 3px solid #e2e8f0;
}

.cleanup-option.advanced {
    border-left: 3px solid #e2e8f0;
}

.cleanup-option[data-impact="high"] {
    border-left: 3px solid #e2e8f0;
}

/* Checkbox Items with Enhanced Layout */
.cleanup-option .checkbox-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    cursor: pointer;
}

.option-icon {
    font-size: 18px;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

.option-text {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
}

.option-text strong {
    color: #1e293b;
    font-weight: 600;
}

/* Badges */
.recommended-badge,
.advanced-badge,
.caution-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.recommended-badge {
    background: #f1f5f9 !important;
    color: #64748b !important;
    border: 1px solid #e2e8f0 !important;
}

.advanced-badge {
    background: #f1f5f9 !important;
    color: #64748b !important;
    border: 1px solid #e2e8f0 !important;
}

.caution-badge {
    background: #f1f5f9 !important;
    color: #64748b !important;
    border: 1px solid #e2e8f0 !important;
}

/* Count Indicators */
.cleanup-option .count {
    font-weight: 700;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-left: auto;
}

.count.high {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.count.medium {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.count.low {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

/* Enhanced Descriptions */
.cleanup-option .description {
    margin-top: 8px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 13px;
    line-height: 1.5;
    color: #64748b;
}

/* Impact Information */
.impact-info {
    display: flex;
    gap: 12px;
    margin-top: 8px;
    font-size: 11px;
}

.impact-level,
.safety-level {
    padding: 2px 6px;
    border-radius: 6px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    font-size: 9px;
}

.impact-level.low {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.impact-level.medium {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.impact-level.high {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.safety-level.safe {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.safety-level.review {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.safety-level.caution {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

/* Sub-options */
.sub-option {
    margin-top: 12px;
    padding: 12px;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.sub-option label {
    font-weight: 600;
    color: #374151;
    margin: 0;
}

.sub-option input[type="number"] {
    width: 80px;
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
}

.help-text {
    color: #6b7280;
    font-size: 11px;
    font-style: italic;
    margin-left: 8px;
}

/* Cleanup Summary Footer */
.cleanup-summary-footer {
    margin-top: 24px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.summary-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 13px;
    font-weight: 600;
}

.selected-count {
    color: #64748b;
}

.estimated-impact {
    color: #64748b;
}

.safety-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #64748b;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #e2e8f0;
}

.safety-notice .dashicons {
    color: #64748b;
    font-size: 16px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .cleanup-summary {
        flex-direction: column;
        gap: 8px;
    }

    .summary-stats {
        flex-direction: column;
        gap: 4px;
    }

    .card-header-actions {
        flex-direction: column;
        gap: 4px;
        margin-top: 8px;
        margin-left: 0;
    }

    .option-text {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .sub-option {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* Interactive Features for Cleanup Options */

/* Button States */
.card-header-actions .button.button-primary {
    background: #4caf50;
    border-color: #4caf50;
    color: white;
}

.card-header-actions .button.button-secondary {
    background: #f1f5f9;
    border-color: #d1d5db;
    color: #374151;
}

.card-header-actions .button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Selection Highlight Animation */
.selection-highlight {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%) !important;
    transform: scale(1.02);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

/* Option Hover Effects */
.option-hover {
    background: #fafbfc !important;
    border-color: #4caf50 !important;
}

/* Impact Level Colors for Summary */
.estimated-impact.impact-low {
    color: #64748b;
}

.estimated-impact.impact-medium {
    color: #64748b;
}

.estimated-impact.impact-high {
    color: #64748b;
    font-weight: 600;
}

/* Enhanced Tooltips */
.cleanup-tooltip {
    position: fixed;
    background: #1e293b;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    max-width: 250px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    pointer-events: none;
    opacity: 0.95;
}

.cleanup-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #1e293b;
}

/* Checkbox Focus States */
.cleanup-option input[type="checkbox"]:focus {
    outline: 2px solid #4caf50;
    outline-offset: 2px;
}

/* Enhanced Count Animations */
.count {
    transition: all 0.2s ease;
}

.cleanup-option:hover .count {
    transform: scale(1.1);
}

/* Button Loading States */
.card-header-actions .button.loading {
    position: relative;
    color: transparent;
}

.card-header-actions .button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Summary Footer Enhancements */
.cleanup-summary-footer {
    transition: all 0.3s ease;
}

.cleanup-summary-footer.has-selections {
    border-color: #4caf50;
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .selection-highlight,
    .option-hover,
    .count,
    .cleanup-summary-footer {
        transition: none;
    }

    .card-header-actions .button:hover {
        transform: none;
    }

    .cleanup-option:hover .count {
        transform: none;
    }
}

/* Cache Management Enhancements */
.cache-layers-info {
    margin-top: 15px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #4CAF50;
}

.cache-info-title {
    margin: 0 0 8px 0;
    font-size: 13px;
    font-weight: 600;
    color: #333;
}

.cache-layers-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.cache-layers-list li {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;
    color: #666;
}

.cache-layers-list li .dashicons {
    color: #4CAF50;
    font-size: 14px;
    margin-right: 6px;
    width: 14px;
    height: 14px;
}

.advanced-stats-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
}

.stats-subtitle {
    margin: 0 0 10px 0;
    font-size: 13px;
    font-weight: 600;
    color: #333;
}

.advanced-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.advanced-stat-item {
    text-align: center;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.advanced-stat-item .stat-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #4CAF50;
    margin-bottom: 2px;
}

.advanced-stat-item .stat-label {
    display: block;
    font-size: 11px;
    color: #666;
    line-height: 1.2;
}

.cache-performance-summary {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
}

.performance-indicator {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
}

.performance-indicator.performance-excellent {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #4caf50;
}

.performance-indicator.performance-good {
    background: #fff3e0;
    color: #f57c00;
    border: 1px solid #ff9800;
}

.performance-indicator.performance-fair {
    background: #fff8e1;
    color: #f9a825;
    border: 1px solid #ffc107;
}

.performance-indicator.performance-poor {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #f44336;
}

.performance-indicator .indicator-icon {
    margin-right: 6px;
    font-size: 16px;
}

/* Enhanced Module Cards - Universal Styling */

/* Collapsible Section System */
.redco-card.collapsible {
    transition: all 0.3s ease;
}

.redco-card.collapsible.collapsed .card-content {
    display: none;
}

.redco-card.collapsible .card-header {
    cursor: pointer;
    position: relative;
}

.redco-card.collapsible .card-header::after {
    content: '\f140';
    font-family: dashicons;
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #6b7280;
    transition: transform 0.3s ease;
}

.redco-card.collapsible.collapsed .card-header::after {
    transform: translateY(-50%) rotate(180deg);
}

.redco-card.collapsible .card-header:hover::after {
    color: var(--redco-primary);
}

/* Compact Mode */
.redco-module-content.compact-mode .redco-card {
    margin-bottom: 16px;
}

.redco-module-content.compact-mode .card-content {
    padding: 16px;
}

.redco-module-content.compact-mode .option-description {
    font-size: 12px;
    line-height: 1.4;
}

.redco-module-content.compact-mode .option-stats {
    margin-top: 8px;
    gap: 8px;
}

/* View Mode Toggle */
.view-mode-controls {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    z-index: 1000;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.view-mode-btn {
    padding: 8px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
    font-size: 16px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-mode-btn:hover {
    background: #f3f4f6;
    color: var(--redco-primary);
}

.view-mode-btn.active {
    background: var(--redco-primary);
    color: white;
}

/* Section Navigation */
.section-navigation {
    position: sticky;
    top: 32px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 24px;
    z-index: 100;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-nav-title {
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.section-nav-links {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.section-nav-link {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.section-nav-link:hover {
    background: #f3f4f6;
    color: var(--redco-primary);
    text-decoration: none;
}

.section-nav-link.active {
    background: var(--redco-primary);
    color: white;
    border-color: var(--redco-primary);
}

/* Progress Indicator */
.reading-progress {
    position: fixed;
    top: 32px;
    left: 0;
    width: 100%;
    height: 3px;
    background: #f1f5f9;
    z-index: 1001;
}

.reading-progress-bar {
    height: 100%;
    background: var(--redco-primary);
    width: 0%;
    transition: width 0.1s ease;
}

/* Settings Introduction Sections */
.settings-intro {
    margin-bottom: 24px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
}

.performance-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.indicator-label {
    font-weight: 600;
    color: #374151;
    font-size: 13px;
}

.impact-level {
    padding: 3px 8px;
    border-radius: 8px;
    font-size: 9px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.impact-level.high {
    background: #166534;
    color: #ffffff;
    border: 1px solid #14532d;
}

.impact-level.medium {
    background: #92400e;
    color: #ffffff;
    border: 1px solid #78350f;
}

.impact-level.low {
    background: #1e40af;
    color: #ffffff;
    border: 1px solid #1e3a8a;
}

/* Enhanced Settings Sections - WP Rocket Style */
.settings-section {
    margin-bottom: 32px;
}

.section-title {
    margin: 0 0 20px 0;
    padding: 16px 20px;
    background: var(--redco-bg-section);
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    font-size: 16px;
    font-weight: 600;
    color: var(--redco-text);
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: var(--redco-shadow);
}

.section-title .dashicons {
    color: var(--redco-primary);
    font-size: 18px;
    width: 18px;
    height: 18px;
}

/* Enhanced Setting Items - WP Rocket Style */
.setting-item.enhanced {
    padding: 24px;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    margin-bottom: 20px;
    background: var(--redco-bg-section);
    transition: all 0.2s ease;
    box-shadow: var(--redco-shadow);
}

.setting-item.enhanced:hover {
    border-color: var(--redco-primary);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.setting-label {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    margin-bottom: 16px;
    flex-wrap: nowrap !important;
    white-space: nowrap;
}

.setting-label strong {
    color: var(--redco-text);
    font-size: 16px;
    font-weight: 600;
    display: inline !important;
    margin: 0 !important;
    padding: 0 !important;
    white-space: nowrap;
    line-height: 1.4;
}

/* Enhanced Help Tooltip Styling - WP Rocket Style */
.help-tooltip {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 20px !important;
    height: 20px !important;
    background: var(--redco-text-light) !important;
    color: white !important;
    border-radius: 50% !important;
    font-size: 12px !important;
    font-weight: bold !important;
    cursor: help !important;
    transition: all 0.2s ease !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
    flex-shrink: 0 !important;
    vertical-align: middle !important;
    white-space: nowrap !important;
    border: 2px solid transparent !important;
}

.help-tooltip:hover {
    background: var(--redco-primary) !important;
    transform: scale(1.05) !important;
    border-color: rgba(76, 175, 80, 0.2) !important;
}

/* Force all setting labels to display inline with tooltips */
.setting-label:not(.checkbox-label) {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    flex-wrap: nowrap !important;
    white-space: nowrap !important;
}

/* Ensure strong elements within labels stay inline */
.setting-label:not(.checkbox-label) strong {
    display: inline !important;
    flex-shrink: 0 !important;
}

/* Ensure help tooltips within labels stay inline */
.setting-label:not(.checkbox-label) .help-tooltip {
    display: inline-flex !important;
    flex-shrink: 0 !important;
}

/* Additional overrides to prevent line breaks */
.setting-label strong,
.setting-label .help-tooltip {
    float: none !important;
    clear: none !important;
    position: static !important;
}

/* Prevent any inherited block display */
.setting-label * {
    display: inline !important;
}

.setting-label .help-tooltip {
    display: inline-flex !important;
}

/* Force inline layout for all label children */
.setting-label > * {
    display: inline !important;
    vertical-align: middle !important;
}

.setting-label > .help-tooltip {
    display: inline-flex !important;
    vertical-align: middle !important;
}

/* Enhanced Inline Benefit Items Styling - WP Rocket Style */
.setting-benefits {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
    margin-top: 12px !important;
    align-items: center !important;
}

.benefit-item {
    display: inline-flex !important;
    align-items: center !important;
    background: var(--redco-bg-subtle) !important;
    border: 1px solid var(--redco-border) !important;
    border-radius: 4px !important;
    padding: 4px 10px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    color: var(--redco-text) !important;
    white-space: nowrap !important;
    transition: all 0.2s ease !important;
    margin: 0 !important;
}

.benefit-item:hover {
    background: #f1f5f9 !important;
    border-color: var(--redco-primary) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1) !important;
    color: var(--redco-primary) !important;
}

/* Sidebar Action Buttons Styling */
.sidebar-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.sidebar-action-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.sidebar-action-button {
    width: 100%;
    padding: 10px 12px;
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-dark) 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.sidebar-action-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.sidebar-action-button:active {
    transform: translateY(0);
}

.sidebar-action-button .dashicons {
    font-size: 16px;
}

.sidebar-action-description {
    font-size: 11px;
    color: #6b7280;
    margin: 0;
    text-align: center;
    line-height: 1.3;
}

.sidebar-test-result {
    margin-top: 12px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.4;
}

.sidebar-test-result.success {
    background: #dcfce7;
    border: 1px solid #bbf7d0;
    color: #166534;
}

.sidebar-test-result.error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.setting-control {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.enhanced-select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.enhanced-select:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.help-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: #6b7280;
    color: white;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
    cursor: help;
    transition: all 0.2s ease;
}

.help-icon:hover {
    background: var(--redco-primary);
    transform: scale(1.1);
}

.setting-description {
    color: #6b7280;
    font-size: 13px;
    line-height: 1.5;
}

.setting-impact {
    display: flex;
    gap: 16px;
    margin-top: 8px;
    font-size: 12px;
}

.impact-info,
.freshness-info {
    display: flex;
    align-items: center;
    gap: 4px;
}

.impact-label,
.freshness-label {
    font-weight: 600;
    color: #374151;
}

.impact-value,
.freshness-value {
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.impact-value {
    background: #dcfce7;
    color: #166534;
}

.freshness-value {
    background: #fef3c7;
    color: #92400e;
}

/* Page Cache Exclusion Controls */
.exclusion-intro {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
}

.exclusion-summary {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    font-size: 13px;
}

.total-pages {
    color: #2e7d32;
    font-weight: 600;
}

.excluded-count {
    color: #666;
}

.exclusion-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 16px;
}

.filter-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-label {
    font-size: 13px;
    color: #374151;
    cursor: pointer;
}

.pages-list-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
}

.pages-list {
    padding: 0;
}

.page-exclusion-item {
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.page-exclusion-item:last-child {
    border-bottom: none;
}

.page-exclusion-item:hover {
    background: #fafbfc;
}

.page-exclusion-item.excluded {
    background: #fef2f2;
    border-left: 3px solid #ef4444;
}

.page-checkbox-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    gap: 12px;
}

.page-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.page-title {
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-meta {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #6b7280;
}

.page-type {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    text-transform: uppercase;
    font-weight: 600;
}

.page-id {
    color: #9ca3af;
}

.exclusion-status {
    margin-left: auto;
}

.status-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-indicator.cached {
    background: #dcfce7;
    color: #166534;
}

.status-indicator.excluded {
    background: #fef2f2;
    color: #dc2626;
}

.exclusion-summary-footer {
    margin-top: 16px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.summary-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 600;
}

.pages-shown {
    color: #4caf50;
}

.cache-efficiency {
    color: #666;
}

.exclusion-tips {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #f57c00;
    background: #fff3e0;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #ff9800;
}

.exclusion-tips .dashicons {
    color: #ff9800;
    font-size: 16px;
}

/* Minification Options */
.minification-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.minification-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.minification-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.minification-option.recommended {
    /* border-left removed */
}

.minification-option.advanced {
    /* border-left removed */
}

.option-checkbox-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
}

.option-icon {
    font-size: 20px;
    width: 28px;
    text-align: center;
    flex-shrink: 0;
    margin-top: 2px;
}

.option-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.option-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.option-title strong {
    color: #1e293b;
    font-size: 15px;
}

.option-description {
    color: #6b7280;
    font-size: 13px;
    line-height: 1.5;
}

.option-stats {
    display: flex;
    gap: 12px;
    margin-top: 12px;
    flex-wrap: wrap;
}

.option-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    padding: 4px 8px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    transition: all 0.2s ease;
    min-width: fit-content;
}

.option-stats .stat-item:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.option-stats .stat-label {
    color: #6b7280 !important;
    font-weight: 500;
    font-size: 8px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.option-stats .stat-value {
    font-weight: 700;
    font-size: 8px;
    background: none;
    border: none;
    padding: 0;
    color: #1e293b !important;
}

/* Specific styling for stat-value with level classes */
.option-stats .stat-value.high,
.option-stats .stat-value.medium,
.option-stats .stat-value.low {
    font-size: 7px !important;
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 600;
}

.option-stats .stat-value.high {
    background: #166534;
    color: #ffffff !important;
    border: 1px solid #14532d;
    font-size: 10px !important;
}

.option-stats .stat-value.medium {
    background: #92400e;
    color: #ffffff !important;
    border: 1px solid #78350f;
}

.option-stats .stat-value.low {
    background: #6b7280;
    color: #ffffff !important;
    border: 1px solid #4b5563;
}

.minification-summary {
    margin-top: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.summary-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 13px;
    font-weight: 600;
}

.enabled-count {
    color: #4caf50;
}

.estimated-savings {
    color: #666;
}

.compatibility-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #f57c00;
    background: #fff3e0;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #ff9800;
}

.compatibility-notice .dashicons {
    color: #ff9800;
    font-size: 16px;
}

/* Small Badges */
.recommended-badge.small,
.advanced-badge.small {
    font-size: 9px !important;
    padding: 1px 4px !important;
    border-radius: 6px !important;
}

/* Lazy Load Module Specific Styles */

/* Threshold Slider Container */
.threshold-slider-container {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.threshold-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
}

.threshold-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--redco-primary);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.threshold-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--redco-primary);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.threshold-input {
    width: 80px;
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
}

.threshold-unit {
    font-size: 13px;
    color: #6b7280;
    font-weight: 600;
}

/* Threshold Recommendations */
.threshold-recommendations {
    display: flex;
    gap: 12px;
    margin-top: 12px;
    flex-wrap: wrap;
}

.threshold-preset {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
    min-width: 80px;
}

.threshold-preset:hover {
    border-color: var(--redco-primary);
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
}

.threshold-preset.active {
    border-color: var(--redco-primary);
    background: #f0f9ff;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.threshold-preset.recommended {
    border-color: var(--redco-primary);
    background: #f8fdf8;
}

.threshold-preset.preset-selected {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.preset-label {
    font-size: 11px;
    font-weight: 600;
    color: #374151;
    text-align: center;
    margin-bottom: 2px;
}

.preset-value {
    font-size: 13px;
    font-weight: 700;
    color: var(--redco-primary);
}

/* Smoothness Info */
.smoothness-info {
    display: flex;
    align-items: center;
    gap: 4px;
}

.smoothness-label {
    font-weight: 600;
    color: #374151;
}

.smoothness-value {
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    background: #e0f2fe;
    color: #0277bd;
}

/* Exclusion Options (Lazy Load specific) */
.exclusion-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.exclusion-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.exclusion-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.exclusion-option.recommended {
    border-left: 3px solid #4caf50;
}

/* Advanced Exclusions */
.advanced-exclusions {
    margin-top: 24px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.advanced-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

.advanced-title .dashicons {
    color: #6b7280;
    font-size: 16px;
}

.advanced-content {
    color: #6b7280;
    font-size: 13px;
}

.code-examples {
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.code-example {
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-label {
    font-weight: 600;
    color: #374151;
    min-width: 80px;
    font-size: 12px;
}

.code-snippet {
    background: #1e293b;
    color: #e2e8f0;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    flex: 1;
}

/* Lazy Load custom notifications removed - using global toast system only */

/* Loading States for Lazy Load */
.button.loading {
    position: relative;
    color: transparent !important;
}

.button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive Design for Lazy Load */
@media (max-width: 768px) {
    .threshold-recommendations {
        justify-content: center;
    }

    .threshold-preset {
        min-width: 70px;
        padding: 6px 8px;
    }

    .threshold-slider-container {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .code-example {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .code-label {
        min-width: auto;
    }
}

/* Heartbeat Control Module Specific Styles */

/* Heartbeat Sections */
.heartbeat-sections {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.heartbeat-section {
    padding: 20px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.heartbeat-section:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Heartbeat Control Groups */
.heartbeat-control-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
    transition: all 0.3s ease;
}

.heartbeat-control-group.group-hover {
    background: #fafbfc;
    border-radius: 6px;
    padding: 12px;
    margin: -12px;
}

.heartbeat-control-group.settings-changed {
    background: #f0f9ff;
    border-radius: 6px;
    padding: 12px;
    margin: -12px;
    border: 1px solid #0ea5e9;
}

.heartbeat-control-group.settings-updated {
    background: #f0fdf4;
    border-radius: 6px;
    padding: 12px;
    margin: -12px;
    border: 1px solid #22c55e;
}

/* Control Main */
.control-main {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
}

.control-label {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.control-label strong {
    color: #1e293b;
    font-size: 15px;
}

.control-description {
    color: #6b7280;
    font-size: 13px;
    font-style: italic;
}

.heartbeat-control-select {
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    transition: all 0.2s ease;
}

.heartbeat-control-select:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* Frequency Controls */
.frequency-control {
    display: none;
    padding: 12px 16px;
    background: #f8fafc;
    border-radius: 6px;
    border-left: 3px solid var(--redco-primary);
    margin-left: 20px;
}

.frequency-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 13px;
}

.frequency-select {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
    background: white;
}

.frequency-select:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

/* Control Info */
.control-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.info-label {
    font-weight: 600;
    color: #6b7280;
}

.info-value {
    color: #374151;
}

.info-value.recommended {
    color: var(--redco-primary);
    font-weight: 600;
}

/* Heartbeat Summary */
.heartbeat-summary {
    margin-top: 24px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.summary-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 13px;
    font-weight: 600;
}

.performance-savings {
    color: var(--redco-primary);
}

.server-load {
    color: #6b7280;
}

.summary-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #f59e0b;
    background: #fff3e0;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #ff9800;
}

.summary-notice .dashicons {
    color: #ff9800;
    font-size: 16px;
}

/* Legacy heartbeat notifications removed - using global toast system only */

/* Loading States for Heartbeat */
.button.loading {
    position: relative;
    color: transparent !important;
}

.button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive Design for Heartbeat */
@media (max-width: 768px) {
    .control-main {
        flex-direction: column;
        gap: 12px;
    }

    .heartbeat-control-select {
        min-width: auto;
        width: 100%;
    }

    .summary-stats {
        flex-direction: column;
        gap: 8px;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .frequency-control {
        margin-left: 0;
    }
}

/* Database Cleanup - Automatic Cleanup Card Styles */

/* Automation Introduction */
.automation-intro {
    margin-bottom: 24px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    border-left: 4px solid #e2e8f0;
}

.automation-status {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-top: 12px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    font-size: 13px;
}

.status-label {
    font-weight: 600;
    color: #374151;
}

.status-indicator {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator.enabled {
    background: #f1f5f9;
    color: #64748b;
}

.status-indicator.disabled {
    background: #f1f5f9;
    color: #64748b;
}

.next-cleanup {
    color: #6b7280;
    font-size: 12px;
}

.next-cleanup strong {
    color: var(--redco-primary);
}

/* Automation Settings */
.automation-settings {
    margin-bottom: 24px;
}

.automation-control-group {
    display: flex;
    flex-direction: column;
    gap: 20px;
    transition: all 0.3s ease;
}

.automation-control-group.settings-updated {
    background: #f0fdf4;
    border-radius: 8px;
    padding: 16px;
    margin: -16px;
    border: 1px solid #22c55e;
}

/* Toggle Switch */
.automation-toggle {
    transition: all 0.3s ease;
}

.automation-toggle.toggle-changed {
    background: #f0f9ff;
    border-radius: 8px;
    padding: 12px;
    margin: -12px;
    border: 1px solid #0ea5e9;
}

.toggle-switch {
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.toggle-switch:hover {
    border-color: var(--redco-primary);
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
}

.toggle-input {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: #cbd5e1;
    border-radius: 12px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-input:checked + .toggle-slider {
    background: var(--redco-primary);
}

.toggle-input:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-label {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.toggle-label strong {
    color: #1e293b;
    font-size: 15px;
}

.toggle-description {
    color: #6b7280;
    font-size: 13px;
}

/* Schedule Settings */
.schedule-settings {
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.schedule-control {
    margin-bottom: 20px;
}

.schedule-label {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 16px;
}

.schedule-label strong {
    color: #1e293b;
    font-size: 15px;
}

.schedule-description {
    color: #6b7280;
    font-size: 13px;
}

/* Schedule Options */
.schedule-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.schedule-option {
    position: relative;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
    cursor: pointer;
}

.schedule-option:hover {
    border-color: var(--redco-primary);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.schedule-option.selected {
    border-color: var(--redco-primary);
    background: #f8fdf8;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.schedule-option.option-selected {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.schedule-option input[type="radio"] {
    display: none;
}

.schedule-option-label {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    cursor: pointer;
}

.option-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.option-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.option-title {
    font-weight: 600;
    color: #1e293b;
    font-size: 14px;
}

.option-description {
    color: #6b7280;
    font-size: 12px;
}

.option-recommendation {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 4px;
    align-self: flex-start;
}

.option-recommendation.recommended {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.option-recommendation.high-traffic {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.option-recommendation.low-traffic {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

/* Schedule Info */
.schedule-info {
    padding: 16px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-label {
    font-size: 11px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 13px;
    font-weight: 600;
    color: #1e293b;
}

.info-value.status-active {
    color: var(--redco-primary);
}

.info-value.status-inactive {
    color: #dc2626;
}

/* Automation Benefits */
.automation-benefits {
    margin-bottom: 24px;
}

.benefits-title {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.benefits-title .dashicons {
    color: var(--redco-primary);
    font-size: 16px;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.benefit-item:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.benefit-icon {
    font-size: 20px;
    flex-shrink: 0;
    margin-top: 2px;
}

.benefit-text {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.benefit-text strong {
    color: #1e293b;
    font-size: 13px;
}

.benefit-text span {
    color: #6b7280;
    font-size: 12px;
    line-height: 1.4;
}

/* Automation Summary */
.automation-summary {
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.summary-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 13px;
    font-weight: 600;
}

.automation-efficiency {
    color: var(--redco-primary);
}

.maintenance-impact {
    color: #6b7280;
}

.automation-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #64748b;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #e2e8f0;
}

.automation-notice .dashicons {
    color: #64748b;
    font-size: 16px;
}

/* Automation custom notifications removed - using global toast system only */

/* Responsive Design for Automation */
@media (max-width: 768px) {
    .schedule-options {
        grid-template-columns: 1fr;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .summary-stats {
        flex-direction: column;
        gap: 8px;
    }

    .automation-status {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* WordPress Core Tweaks Module Specific Styles */

/* Emoji Options */
.emoji-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.emoji-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.emoji-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.emoji-option.recommended {
    border-left: 3px solid #4caf50;
}

.emoji-option.advanced {
    border-left: 3px solid #ff9800;
}

/* Version Options */
.version-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.version-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.version-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.version-option.recommended {
    border-left: 3px solid #4caf50;
}

.version-option.advanced {
    border-left: 3px solid #ff9800;
}

.version-option.security {
    border-left: 3px solid #2196f3;
}

/* Emoji and Version Summary */
.emoji-summary,
.version-summary {
    margin-top: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.emoji-notice,
.version-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #64748b;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #e2e8f0;
    margin-top: 12px;
}

.emoji-notice .dashicons,
.version-notice .dashicons {
    color: #64748b;
    font-size: 16px;
}

/* Security Badge */
.security-badge {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

/* Exclusion Settings */
.exclusion-settings {
    margin-top: 24px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.exclusion-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

.exclusion-title .dashicons {
    color: #6b7280;
    font-size: 16px;
}

.exclusion-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.exclusion-label {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.exclusion-label strong {
    color: #1e293b;
    font-size: 13px;
}

.exclusion-description {
    color: #6b7280;
    font-size: 12px;
}

.exclusion-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 13px;
    font-family: 'Courier New', monospace;
    background: white;
    resize: vertical;
    min-height: 80px;
}

.exclusion-textarea:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.exclusion-help {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #6b7280;
    background: #f1f5f9;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #94a3b8;
}

.exclusion-help .dashicons {
    color: #94a3b8;
    font-size: 16px;
}

/* Legacy core tweaks notifications removed - using global toast system only */

/* Query String Options */
.query-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.query-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.query-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.query-option.recommended {
    border-left: 3px solid #4caf50;
}

.query-option.advanced {
    border-left: 3px solid #ff9800;
}

/* Query Exclusion Settings */
.query-exclusion-settings {
    margin-top: 24px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.query-exclusion-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

.query-exclusion-title .dashicons {
    color: #6b7280;
    font-size: 16px;
}

.query-exclusion-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.query-exclusion-label {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.query-exclusion-label strong {
    color: #1e293b;
    font-size: 13px;
}

.query-exclusion-description {
    color: #6b7280;
    font-size: 12px;
}

.query-exclusion-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 13px;
    font-family: 'Courier New', monospace;
    background: white;
    resize: vertical;
    min-height: 80px;
}

.query-exclusion-textarea:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.query-exclusion-help {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #6b7280;
    background: #f1f5f9;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #94a3b8;
}

.query-exclusion-help .dashicons {
    color: #94a3b8;
    font-size: 16px;
}

/* Query and Autosave Summary */
.query-summary,
.autosave-summary {
    margin-top: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.query-notice,
.autosave-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #f57c00;
    background: #fff3e0;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #ff9800;
    margin-top: 12px;
}

.query-notice .dashicons,
.autosave-notice .dashicons {
    color: #ff9800;
    font-size: 16px;
}

/* Autosave Options */
.autosave-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.autosave-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.autosave-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.autosave-option.recommended {
    border-left: 3px solid #4caf50;
}

.autosave-option.advanced {
    border-left: 3px solid #ff9800;
}

/* Autosave Settings */
.autosave-settings {
    margin-top: 24px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.autosave-settings-title {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

.autosave-settings-title .dashicons {
    color: #6b7280;
    font-size: 16px;
}

.autosave-settings-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-label {
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    flex-wrap: nowrap !important;
    white-space: nowrap !important;
}

.setting-label strong {
    color: #1e293b;
    font-size: 13px;
    display: inline !important;
    margin: 0 !important;
    padding: 0 !important;
    white-space: nowrap !important;
}

/* Mobile help tooltips remain inline but smaller */
.help-tooltip {
    width: 16px !important;
    height: 16px !important;
    font-size: 10px !important;
    display: inline-flex !important;
    flex-shrink: 0 !important;
}

/* Mobile specific inline enforcement */
.setting-label:not(.checkbox-label) {
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    flex-wrap: nowrap !important;
    white-space: nowrap !important;
}

.setting-label:not(.checkbox-label) strong {
    display: inline !important;
    flex-shrink: 0 !important;
}

.setting-label:not(.checkbox-label) .help-tooltip {
    display: inline-flex !important;
    flex-shrink: 0 !important;
}

/* Mobile responsive benefit items */
.setting-benefits {
    gap: 8px !important;
}

.benefit-item {
    font-size: 11px !important;
    padding: 3px 6px !important;
}

/* Mobile sidebar actions */
.sidebar-action-button {
    padding: 8px 10px !important;
    font-size: 12px !important;
}

.sidebar-action-description {
    font-size: 10px !important;
}

.setting-description {
    color: #6b7280;
    font-size: 12px;
}

.setting-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.setting-input {
    padding: 6px 10px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
    width: 100px;
}

.setting-input:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.setting-unit {
    color: #6b7280;
    font-size: 12px;
    font-weight: 500;
}

.setting-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 11px;
}

.current-setting {
    color: #059669;
    font-weight: 500;
}

.default-setting {
    color: #6b7280;
}

.recommended-setting {
    color: #0ea5e9;
    font-weight: 500;
}

/* Post Types Grid */
.post-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 8px;
}

.post-type-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.post-type-item:hover {
    border-color: #4caf50;
    background: #f0f9ff;
}

.post-type-item input[type="checkbox"] {
    margin: 0;
}

.post-type-name {
    font-weight: 500;
    color: #374151;
    font-size: 12px;
}

.post-type-slug {
    color: #6b7280;
    font-size: 11px;
}

/* Option Warning */
.option-warning {
    margin-top: 12px;
    padding: 8px 12px;
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #92400e;
}

.option-warning .dashicons {
    color: #f59e0b;
    font-size: 16px;
}

/* Responsive Design for Core Tweaks */
@media (max-width: 768px) {
    .exclusion-textarea,
    .query-exclusion-textarea {
        min-height: 60px;
    }

    .exclusion-help,
    .query-exclusion-help {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .post-types-grid {
        grid-template-columns: 1fr;
    }

    .setting-control {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .setting-input {
        width: 100%;
    }
}

/* Critical Resource Optimizer Module Specific Styles */

/* Critical CSS Options */
.critical-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.critical-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.critical-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.critical-option.recommended {
    border-left: 3px solid #4caf50;
}

/* JavaScript Options */
.js-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.js-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.js-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.js-option.recommended {
    border-left: 3px solid #4caf50;
}

/* Font Options */
.font-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.font-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.font-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.font-option.recommended {
    border-left: 3px solid #4caf50;
}

/* Hints Options */
.hints-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.hints-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.hints-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.hints-option.recommended {
    border-left: 3px solid #4caf50;
}

.hints-option.advanced {
    border-left: 3px solid #ff9800;
}

/* Option Details */
.option-details {
    margin-top: 12px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

.detail-value {
    font-size: 12px;
    color: #374151;
    font-weight: 600;
}

/* Critical, JS, Font, and Hints Summary */
.critical-summary,
.js-summary,
.font-summary,
.hints-summary {
    margin-top: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.critical-notice,
.js-notice,
.font-notice,
.hints-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #f57c00;
    background: #fff3e0;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #ff9800;
    margin-top: 12px;
}

.critical-notice .dashicons,
.js-notice .dashicons,
.font-notice .dashicons,
.hints-notice .dashicons {
    color: #ff9800;
    font-size: 16px;
}

/* Custom Hints Section */
.custom-hints-section {
    margin-top: 24px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.custom-hints-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

.custom-hints-title .dashicons {
    color: #6b7280;
    font-size: 16px;
}

.custom-hints-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.custom-hints-label {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.custom-hints-label strong {
    color: #1e293b;
    font-size: 13px;
}

.custom-hints-description {
    color: #6b7280;
    font-size: 12px;
}

.custom-hints-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 13px;
    font-family: 'Courier New', monospace;
    background: white;
    resize: vertical;
    min-height: 80px;
}

.custom-hints-textarea:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.custom-hints-help {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #6b7280;
    background: #f1f5f9;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #94a3b8;
}

.custom-hints-help .dashicons {
    color: #94a3b8;
    font-size: 16px;
}

/* Critical Resource Optimizer custom notifications removed - using global toast system only */

/* Advanced Options */
.advanced-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.advanced-option {
    padding: 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.advanced-option:hover {
    border-color: #4caf50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.advanced-option.recommended {
    border-left: 3px solid #4caf50;
}

.advanced-option.advanced {
    border-left: 3px solid #ff9800;
}

.advanced-option.development {
    border-left: 3px solid #2196f3;
}

.advanced-option.expert {
    border-left: 3px solid #9c27b0;
}

/* Development Badge */
.development-badge {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: #f1f5f9 !important;
    color: #64748b !important;
    border: 1px solid #e2e8f0 !important;
}

/* Expert Badge */
.expert-badge {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: #f1f5f9 !important;
    color: #64748b !important;
    border: 1px solid #e2e8f0 !important;
}

/* Advanced Configuration */
.advanced-configuration {
    margin-top: 24px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.advanced-config-title {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

.advanced-config-title .dashicons {
    color: #6b7280;
    font-size: 16px;
}

.config-groups {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.config-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.config-label {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.config-label strong {
    color: #1e293b;
    font-size: 13px;
}

.config-description {
    color: #6b7280;
    font-size: 12px;
}

.config-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-input {
    padding: 6px 10px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
    width: 100px;
}

.config-input:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.config-select {
    padding: 6px 10px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
    background: white;
    min-width: 150px;
}

.config-select:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.config-unit {
    color: #6b7280;
    font-size: 12px;
    font-weight: 500;
}

.config-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 11px;
}

.config-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 13px;
    font-family: 'Courier New', monospace;
    background: white;
    resize: vertical;
    min-height: 80px;
}

.config-textarea:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.config-help {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #6b7280;
    background: #f1f5f9;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #94a3b8;
    margin-top: 8px;
}

.config-help .dashicons {
    color: #94a3b8;
    font-size: 16px;
}

/* Advanced Summary */
.advanced-summary {
    margin-top: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.advanced-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #f57c00;
    background: #fff3e0;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #ff9800;
    margin-top: 12px;
}

.advanced-notice .dashicons {
    color: #ff9800;
    font-size: 16px;
}

/* Selection Highlight Animation */
.selection-highlight {
    animation: highlightPulse 0.6s ease-in-out;
}

@keyframes highlightPulse {
    0% {
        background-color: rgba(76, 175, 80, 0.1);
        transform: scale(1);
    }
    50% {
        background-color: rgba(76, 175, 80, 0.2);
        transform: scale(1.02);
    }
    100% {
        background-color: rgba(76, 175, 80, 0.1);
        transform: scale(1);
    }
}

/* Mobile Responsive Optimizations */
@media (max-width: 768px) {
    /* Enhanced Header Mobile Responsive */
    .header-breadcrumb {
        padding: 6px 20px;
        font-size: 11px;
    }

    .header-content {
        padding: 16px 20px;
        display: flex;
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .header-main {
        width: 100%;
        gap: 16px;
        align-items: flex-start;
    }

    .header-icon {
        padding: 16px;
        border-radius: 12px;
    }

    .header-icon .dashicons {
        font-size: 28px;
        width: 28px;
        height: 28px;
    }

    .header-text h1 {
        font-size: 24px;
        margin-bottom: 6px;
    }

    .header-text p {
        font-size: 14px;
        margin-bottom: 12px;
    }

    .header-status {
        gap: 8px;
        margin-top: 8px;
    }

    .status-badge {
        padding: 4px 8px;
        font-size: 10px;
        border-radius: 16px;
    }

    .header-actions {
        width: 100%;
        align-items: stretch;
        min-width: auto;
    }

    .header-action-group {
        align-items: stretch;
        width: 100%;
    }

    .header-quick-actions {
        grid-template-columns: 1fr 1fr;
        gap: 6px;
        width: 100%;
    }

    .header-action-btn {
        padding: 8px 6px;
        font-size: 10px;
        gap: 3px;
    }

    .header-action-btn .dashicons {
        font-size: 12px;
        width: 12px;
        height: 12px;
    }



    .header-metrics {
        grid-template-columns: repeat(3, 1fr);
        gap: 6px;
        width: 100%;
    }

    .header-metric {
        padding: 6px 4px;
    }

    .header-metric-value {
        font-size: 16px;
    }

    .header-metric-label {
        font-size: 9px;
    }

    /* Hide view mode controls on mobile */
    .view-mode-controls {
        display: none;
    }

    /* Auto-enable compact mode on mobile */
    .redco-module-content {
        padding: 12px;
    }

    .redco-module-content .redco-card {
        margin-bottom: 12px;
    }

    .redco-module-content .card-content {
        padding: 12px;
    }

    /* Simplify section navigation */
    .section-navigation {
        position: relative;
        top: auto;
        padding: 8px 12px;
        margin-bottom: 16px;
    }

    .section-nav-links {
        gap: 8px;
    }

    .section-nav-link {
        font-size: 11px;
        padding: 3px 6px;
    }

    /* Collapse advanced sections by default on mobile */
    .redco-card.collapsible.advanced,
    .redco-card.collapsible.expert {
        .card-content {
            display: none;
        }
    }

    .redco-card.collapsible.advanced.collapsed,
    .redco-card.collapsible.expert.collapsed {
        .card-header::after {
            transform: translateY(-50%) rotate(180deg);
        }
    }

    /* Responsive form elements */
    .custom-hints-textarea,
    .config-textarea {
        min-height: 60px;
    }

    .custom-hints-help,
    .config-help {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .option-details {
        padding: 8px;
    }

    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }

    .config-control {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .config-input,
    .config-select {
        width: 100%;
        min-width: auto;
    }

    .config-groups {
        gap: 16px;
    }

    /* Compact option stats on mobile */
    .option-stats {
        gap: 8px;
    }

    .option-stats .stat-item {
        padding: 2px 6px;
        font-size: 9px;
    }

    .option-stats .stat-label,
    .option-stats .stat-value {
        font-size: 8px;
    }
}

/* Trending Pages - Compact */
.trending-pages-list {
    margin-bottom: 8px;
}

.trending-page-item {
    padding: 6px 0;
    border-bottom: 1px solid #f0f0f1;
}

.trending-page-item:last-child {
    border-bottom: none;
}

.page-info .page-title {
    font-size: 0.8em;
    font-weight: 500;
    color: #1d2327;
    margin-bottom: 3px;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.page-stats {
    display: flex;
    gap: 6px;
    font-size: 0.7em;
    color: #646970;
}

.page-stats span {
    background: #f6f7f7;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 0.7em;
    color: #1d2327;
    font-weight: 500;
}

/* Optimization Tips - Compact */
.optimization-tips-list {
    margin-bottom: 8px;
}

.optimization-tip {
    padding: 6px 8px;
    margin-bottom: 6px;
    border-radius: 3px;
    border-left: 2px solid;
}

.optimization-tip.tip-warning {
    background: #fff8e1;
    border-left-color: #ff9800;
}

.optimization-tip.tip-info {
    background: #e3f2fd;
    border-left-color: #2196f3;
}

.optimization-tip.tip-success {
    background: #e8f5e8;
    border-left-color: #4caf50;
}

.tip-header {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 2px;
    font-size: 0.75em;
}

.tip-header strong {
    color: #1d2327;
    font-weight: 600;
}

.tip-icon {
    font-size: 12px;
    color: #1d2327;
}

.tip-message {
    font-size: 0.7em;
    line-height: 1.3;
    color: #1d2327;
    font-weight: 400;
}

/* Historical Trends - Compact */
.trends-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 6px;
    background: #f6f7f7;
    border-radius: 3px;
}

.trend-stat {
    text-align: center;
}

.trend-value {
    display: block;
    font-size: 0.9em;
    font-weight: 600;
    color: #1d2327;
    line-height: 1.1;
}

.trend-label {
    font-size: 0.65em;
    color: #646970;
    font-weight: 500;
}

.trend-direction {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 0.7em;
    font-weight: 500;
}

.trend-direction.trend-increasing {
    color: #4caf50;
}

.trend-direction.trend-decreasing {
    color: #f44336;
}

.trend-direction.trend-stable {
    color: #646970;
}

.trends-chart {
    margin-bottom: 8px;
}

.chart-bars {
    display: flex;
    align-items: end;
    gap: 1px;
    height: 30px;
    padding: 3px;
    background: #f9f9f9;
    border-radius: 3px;
}

.chart-bar {
    flex: 1;
    background: linear-gradient(to top, #4caf50, #81c784);
    border-radius: 1px 1px 0 0;
    min-height: 2px;
    transition: all 0.2s ease;
}

.chart-bar:hover {
    background: linear-gradient(to top, #388e3c, #66bb6a);
    transform: scaleY(1.05);
}

/* Enhanced Responsive Design for New Sections */
@media (max-width: 768px) {
    .trends-summary {
        flex-direction: column;
        gap: 6px;
        padding: 4px;
    }

    .page-stats {
        flex-direction: column;
        gap: 3px;
    }

    .optimization-tip {
        padding: 4px 6px;
        margin-bottom: 4px;
    }

    .tip-header {
        font-size: 0.7em;
        margin-bottom: 1px;
    }

    .tip-message {
        font-size: 0.65em;
    }

    .sidebar-section-header {
        padding: 8px 12px;
    }

    .sidebar-section-header h3 {
        font-size: 13px;
    }

    .sidebar-section-content {
        padding: 8px 12px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .stat-item {
        padding: 6px 4px;
    }

    .stat-value {
        font-size: 14px;
    }

    .stat-label {
        font-size: 9px;
    }

    /* Maintain high-contrast colors on mobile */
    .stat-hits .stat-value {
        color: #2e7d32 !important;
        font-weight: 700 !important;
    }

    .stat-hits .stat-label {
        color: #1b5e20 !important;
    }

    .stat-misses .stat-value {
        color: #e65100 !important;
        font-weight: 700 !important;
    }

    .stat-misses .stat-label {
        color: #bf360c !important;
    }

    .stat-size .stat-value {
        color: #1565c0 !important;
        font-weight: 700 !important;
    }

    .stat-size .stat-label {
        color: #0d47a1 !important;
    }

    .stat-ratio .stat-value {
        color: #6a1b9a !important;
        font-weight: 700 !important;
    }

    .stat-ratio .stat-label {
        color: #4a148c !important;
    }

    /* Mobile responsive colors now use standardized base colors - no overrides needed */
}