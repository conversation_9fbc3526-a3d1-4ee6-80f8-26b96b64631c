<?php
/**
 * Intelligent Optimizer Class for Redco Optimizer
 *
 * Provides intelligent environment analysis and automatic optimization configuration
 * based on server capabilities, active plugins, and performance metrics.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Intelligent_Optimizer {

    /**
     * Singleton instance
     */
    private static $instance = null;

    /**
     * Analysis results cache
     */
    private $analysis_cache = null;

    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize the intelligent optimizer
     */
    public function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // AJAX handlers
        add_action('wp_ajax_redco_run_intelligent_optimizer', array($this, 'ajax_run_intelligent_optimizer'));
        add_action('wp_ajax_redco_apply_optimizer_recommendations', array($this, 'ajax_apply_recommendations'));
        add_action('wp_ajax_redco_get_optimization_preview', array($this, 'ajax_get_optimization_preview'));
    }

    /**
     * AJAX handler for running the intelligent optimizer
     */
    public function ajax_run_intelligent_optimizer() {
        // Security check
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce') || !current_user_can('manage_options')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Set time limit for analysis
        if (!ini_get('safe_mode')) {
            set_time_limit(60);
        }

        try {
            // Check if helper functions are available
            if (!function_exists('redco_get_option')) {
                wp_send_json_error('Helper functions not available. Please refresh the page and try again.');
                return;
            }

            // Run comprehensive analysis
            $analysis = $this->run_comprehensive_analysis();

            // Validate analysis results
            if (empty($analysis) || !is_array($analysis)) {
                wp_send_json_error('Analysis failed to produce valid results');
                return;
            }

            // Generate recommendations
            $recommendations = $this->generate_recommendations($analysis);

            // Calculate performance score
            $performance_score = $this->calculate_performance_score($analysis);

            $response_data = array(
                'analysis' => $analysis,
                'recommendations' => $recommendations,
                'performance_score' => $performance_score,
                'estimated_improvement' => $this->calculate_estimated_improvement($recommendations)
            );

            wp_send_json_success($response_data);

        } catch (Exception $e) {
            wp_send_json_error('Analysis failed: ' . $e->getMessage());
        } catch (Error $e) {
            wp_send_json_error('Fatal error during analysis: ' . $e->getMessage());
        }
    }

    /**
     * Run comprehensive environment analysis
     */
    public function run_comprehensive_analysis() {
        if ($this->analysis_cache !== null) {
            return $this->analysis_cache;
        }

        $analysis = array(
            'server_capabilities' => $this->analyze_server_capabilities(),
            'plugin_conflicts' => $this->detect_plugin_conflicts(),
            'performance_metrics' => $this->measure_performance_metrics(),
            'current_optimizations' => $this->analyze_current_optimizations(),
            'content_analysis' => $this->analyze_content_structure(),
            'bottlenecks' => $this->identify_bottlenecks()
        );

        $this->analysis_cache = $analysis;
        return $analysis;
    }

    /**
     * Analyze server capabilities
     */
    private function analyze_server_capabilities() {
        return array(
            'php_version' => PHP_VERSION,
            'php_version_adequate' => version_compare(PHP_VERSION, '7.4', '>='),
            'memory_limit' => ini_get('memory_limit'),
            'memory_limit_bytes' => $this->convert_to_bytes(ini_get('memory_limit')),
            'memory_adequate' => $this->convert_to_bytes(ini_get('memory_limit')) >= 128 * 1024 * 1024,
            'max_execution_time' => ini_get('max_execution_time'),
            'webp_support' => function_exists('imagewebp') && (imagetypes() & IMG_WEBP),
            'gd_extension' => extension_loaded('gd'),
            'curl_extension' => extension_loaded('curl'),
            'zip_extension' => extension_loaded('zip'),
            'opcache_enabled' => extension_loaded('opcache') && ini_get('opcache.enable'),
            'gzip_enabled' => extension_loaded('zlib'),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'is_shared_hosting' => $this->detect_shared_hosting(),
            'write_permissions' => $this->check_write_permissions()
        );
    }

    /**
     * Detect plugin conflicts
     */
    private function detect_plugin_conflicts() {
        $active_plugins = get_option('active_plugins', array());
        $conflicts = array();

        $known_conflicts = array(
            'wp-rocket/wp-rocket.php' => array(
                'name' => 'WP Rocket',
                'conflicts_with' => array('page-cache', 'css-js-minifier'),
                'severity' => 'high',
                'recommendation' => 'Disable Redco page cache and minification modules'
            ),
            'w3-total-cache/w3-total-cache.php' => array(
                'name' => 'W3 Total Cache',
                'conflicts_with' => array('page-cache'),
                'severity' => 'medium',
                'recommendation' => 'Choose one caching solution'
            ),
            'wp-super-cache/wp-cache.php' => array(
                'name' => 'WP Super Cache',
                'conflicts_with' => array('page-cache'),
                'severity' => 'medium',
                'recommendation' => 'Disable one caching plugin'
            ),
            'autoptimize/autoptimize.php' => array(
                'name' => 'Autoptimize',
                'conflicts_with' => array('css-js-minifier'),
                'severity' => 'medium',
                'recommendation' => 'Choose one minification solution'
            ),
            'wp-smushit/wp-smush.php' => array(
                'name' => 'Smush',
                'conflicts_with' => array('smart-webp-conversion'),
                'severity' => 'low',
                'recommendation' => 'Consider using only one image optimization plugin'
            )
        );

        foreach ($active_plugins as $plugin) {
            if (isset($known_conflicts[$plugin])) {
                $conflicts[] = $known_conflicts[$plugin];
            }
        }

        return $conflicts;
    }

    /**
     * Measure current performance metrics
     */
    private function measure_performance_metrics() {
        $start_time = microtime(true);
        
        // Database performance test
        global $wpdb;
        $db_start = microtime(true);
        $wpdb->get_results("SELECT option_name FROM {$wpdb->options} LIMIT 10");
        $db_time = microtime(true) - $db_start;

        // File system performance test (with error handling)
        $fs_time = 0;
        try {
            $upload_dir = wp_upload_dir();
            if (!$upload_dir['error']) {
                $fs_start = microtime(true);
                $test_file = $upload_dir['basedir'] . '/redco_test_' . time() . '.txt';

                // Ensure directory exists
                if (!file_exists($upload_dir['basedir'])) {
                    wp_mkdir_p($upload_dir['basedir']);
                }

                if (is_writable($upload_dir['basedir'])) {
                    file_put_contents($test_file, 'test');
                    file_get_contents($test_file);
                    if (file_exists($test_file)) {
                        unlink($test_file);
                    }
                }
                $fs_time = microtime(true) - $fs_start;
            }
        } catch (Exception $e) {
            // If filesystem test fails, use a default time
            $fs_time = 0.1;
        }

        return array(
            'database_query_time' => round($db_time * 1000, 2),
            'database_performance' => $db_time < 0.05 ? 'excellent' : ($db_time < 0.2 ? 'good' : 'poor'),
            'filesystem_time' => round($fs_time * 1000, 2),
            'filesystem_performance' => $fs_time < 0.1 ? 'excellent' : ($fs_time < 0.5 ? 'good' : 'poor'),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'memory_usage_percentage' => round((memory_get_usage(true) / $this->convert_to_bytes(ini_get('memory_limit'))) * 100, 1),
            'total_queries' => get_num_queries(),
            'analysis_time' => microtime(true) - $start_time
        );
    }

    /**
     * Analyze current optimization status
     */
    private function analyze_current_optimizations() {
        $enabled_modules = redco_get_option('modules_enabled', array());
        $optimizations = array();

        $available_modules = array(
            'page-cache' => array('impact' => 'high', 'priority' => 1),
            'css-js-minifier' => array('impact' => 'medium', 'priority' => 2),
            'smart-webp-conversion' => array('impact' => 'medium', 'priority' => 3),
            'lazy-load' => array('impact' => 'low', 'priority' => 4),
            'database-cleanup' => array('impact' => 'low', 'priority' => 5),
            'heartbeat-control' => array('impact' => 'low', 'priority' => 6),
            'wordpress-core-tweaks' => array('impact' => 'low', 'priority' => 7)
        );

        foreach ($available_modules as $module => $config) {
            $optimizations[$module] = array(
                'enabled' => in_array($module, $enabled_modules),
                'impact' => $config['impact'],
                'priority' => $config['priority'],
                'working' => $this->test_module_functionality($module)
            );
        }

        return $optimizations;
    }

    /**
     * Analyze content structure for optimization opportunities
     */
    private function analyze_content_structure() {
        global $wpdb;

        // Get image statistics
        $total_images = $wpdb->get_var("
            SELECT COUNT(*) FROM {$wpdb->posts} 
            WHERE post_type = 'attachment' 
            AND post_mime_type LIKE 'image/%'
        ");

        $large_images = $wpdb->get_var("
            SELECT COUNT(*) FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment' 
            AND p.post_mime_type LIKE 'image/%'
            AND pm.meta_key = '_wp_attachment_metadata'
            AND pm.meta_value LIKE '%\"width\";i:%'
            AND CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(pm.meta_value, '\"width\";i:', -1), ';', 1) AS UNSIGNED) > 1920
        ");

        return array(
            'total_images' => intval($total_images),
            'large_images' => intval($large_images),
            'optimization_potential' => $large_images > 0 ? 'high' : ($total_images > 50 ? 'medium' : 'low'),
            'total_posts' => wp_count_posts()->publish,
            'total_pages' => wp_count_posts('page')->publish
        );
    }

    /**
     * Identify performance bottlenecks
     */
    private function identify_bottlenecks() {
        $bottlenecks = array();

        // Prevent circular reference - get analysis data directly
        if ($this->analysis_cache === null) {
            // Get basic analysis data without calling full analysis
            $server_capabilities = $this->analyze_server_capabilities();
            $performance_metrics = $this->measure_performance_metrics();
            $current_optimizations = $this->analyze_current_optimizations();

            $analysis = array(
                'server_capabilities' => $server_capabilities,
                'performance_metrics' => $performance_metrics,
                'current_optimizations' => $current_optimizations
            );
        } else {
            $analysis = $this->analysis_cache;
        }

        // Check for memory issues
        if ($analysis['performance_metrics']['memory_usage_percentage'] > 80) {
            $bottlenecks[] = array(
                'type' => 'memory',
                'severity' => 'high',
                'description' => 'High memory usage detected',
                'recommendation' => 'Increase PHP memory limit or optimize plugins'
            );
        }

        // Check for database performance issues
        if ($analysis['performance_metrics']['database_performance'] === 'poor') {
            $bottlenecks[] = array(
                'type' => 'database',
                'severity' => 'medium',
                'description' => 'Slow database queries detected',
                'recommendation' => 'Enable database optimization and cleanup'
            );
        }

        // Check for missing critical optimizations
        if (!$analysis['current_optimizations']['page-cache']['enabled']) {
            $bottlenecks[] = array(
                'type' => 'caching',
                'severity' => 'high',
                'description' => 'No page caching enabled',
                'recommendation' => 'Enable page cache module for significant performance improvement'
            );
        }

        return $bottlenecks;
    }

    /**
     * Convert memory string to bytes
     */
    private function convert_to_bytes($memory_string) {
        $memory_string = trim($memory_string);
        $last = strtolower($memory_string[strlen($memory_string) - 1]);
        $number = (int) $memory_string;

        switch ($last) {
            case 'g':
                $number *= 1024;
            case 'm':
                $number *= 1024;
            case 'k':
                $number *= 1024;
        }

        return $number;
    }

    /**
     * Detect if running on shared hosting
     */
    private function detect_shared_hosting() {
        $indicators = array(
            'cpanel' => isset($_SERVER['HTTP_HOST']) && strpos($_SERVER['HTTP_HOST'], 'cpanel') !== false,
            'shared_memory' => $this->convert_to_bytes(ini_get('memory_limit')) <= 256 * 1024 * 1024,
            'execution_time' => ini_get('max_execution_time') <= 30,
            'common_shared_hosts' => isset($_SERVER['SERVER_NAME']) && (
                strpos($_SERVER['SERVER_NAME'], 'hostgator') !== false ||
                strpos($_SERVER['SERVER_NAME'], 'bluehost') !== false ||
                strpos($_SERVER['SERVER_NAME'], 'godaddy') !== false
            )
        );

        return array_sum($indicators) >= 2;
    }

    /**
     * Check write permissions for cache directories
     */
    private function check_write_permissions() {
        $upload_dir = wp_upload_dir();
        $cache_dir = $upload_dir['basedir'] . '/redco-cache/';
        
        if (!file_exists($cache_dir)) {
            wp_mkdir_p($cache_dir);
        }

        return is_writable($cache_dir);
    }

    /**
     * Test module functionality
     */
    private function test_module_functionality($module) {
        if (!redco_is_module_enabled($module)) {
            return false;
        }

        switch ($module) {
            case 'page-cache':
                return $this->check_write_permissions();
            case 'smart-webp-conversion':
                return function_exists('imagewebp') && (imagetypes() & IMG_WEBP);
            case 'css-js-minifier':
                return true; // Basic functionality always available
            default:
                return true;
        }
    }

    /**
     * Generate intelligent recommendations based on analysis
     */
    public function generate_recommendations($analysis) {
        $recommendations = array();
        $conflicts = $analysis['plugin_conflicts'];
        $capabilities = $analysis['server_capabilities'];
        $current_opts = $analysis['current_optimizations'];
        $content = $analysis['content_analysis'];

        // High priority recommendations
        if (!$current_opts['page-cache']['enabled'] && empty($conflicts)) {
            $recommendations[] = array(
                'id' => 'enable_page_cache',
                'title' => 'Enable Page Caching',
                'description' => 'Page caching can improve your site speed by 40-60%. No conflicts detected.',
                'priority' => 'high',
                'impact' => 'high',
                'estimated_improvement' => '40-60%',
                'action' => 'enable_module',
                'module' => 'page-cache',
                'settings' => array(
                    'cache_expiry' => $capabilities['is_shared_hosting'] ? 3600 : 7200,
                    'exclude_logged_in' => true
                ),
                'requirements_met' => $capabilities['write_permissions'],
                'reason' => 'Your server supports caching and no conflicting plugins detected'
            );
        }

        // WebP conversion recommendation
        if (!$current_opts['smart-webp-conversion']['enabled'] && $capabilities['webp_support'] && $content['total_images'] > 10) {
            $recommendations[] = array(
                'id' => 'enable_webp_conversion',
                'title' => 'Enable WebP Image Conversion',
                'description' => 'Convert images to WebP format for 25-35% smaller file sizes.',
                'priority' => 'high',
                'impact' => 'medium',
                'estimated_improvement' => '25-35%',
                'action' => 'enable_module',
                'module' => 'smart-webp-conversion',
                'settings' => array(
                    'auto_convert_uploads' => true,
                    'replace_in_content' => true,
                    'quality' => 85,
                    'batch_size' => $capabilities['is_shared_hosting'] ? 5 : 10
                ),
                'requirements_met' => $capabilities['webp_support'],
                'reason' => sprintf('You have %d images that could benefit from WebP conversion', $content['total_images'])
            );
        }

        // CSS/JS minification
        if (!$current_opts['css-js-minifier']['enabled'] && !$this->has_minification_conflict($conflicts)) {
            $recommendations[] = array(
                'id' => 'enable_minification',
                'title' => 'Enable CSS/JS Minification',
                'description' => 'Reduce file sizes by removing unnecessary whitespace and comments.',
                'priority' => 'medium',
                'impact' => 'medium',
                'estimated_improvement' => '10-20%',
                'action' => 'enable_module',
                'module' => 'css-js-minifier',
                'settings' => array(
                    'minify_css' => true,
                    'minify_js' => true,
                    'combine_files' => !$capabilities['is_shared_hosting']
                ),
                'requirements_met' => true,
                'reason' => 'No conflicting minification plugins detected'
            );
        }

        // Lazy loading for image-heavy sites
        if (!$current_opts['lazy-load']['enabled'] && $content['total_images'] > 20) {
            $recommendations[] = array(
                'id' => 'enable_lazy_load',
                'title' => 'Enable Lazy Loading',
                'description' => 'Load images only when they come into view to improve initial page load.',
                'priority' => 'medium',
                'impact' => 'low',
                'estimated_improvement' => '5-15%',
                'action' => 'enable_module',
                'module' => 'lazy-load',
                'settings' => array(
                    'threshold' => 200,
                    'fade_in' => true
                ),
                'requirements_met' => true,
                'reason' => sprintf('Your site has %d images that could benefit from lazy loading', $content['total_images'])
            );
        }

        // Database cleanup for older sites
        if (!$current_opts['database-cleanup']['enabled'] && $content['total_posts'] > 100) {
            $recommendations[] = array(
                'id' => 'enable_database_cleanup',
                'title' => 'Enable Database Cleanup',
                'description' => 'Remove unnecessary data like spam comments, revisions, and expired transients.',
                'priority' => 'low',
                'impact' => 'low',
                'estimated_improvement' => '2-5%',
                'action' => 'enable_module',
                'module' => 'database-cleanup',
                'settings' => array(
                    'auto_cleanup' => true,
                    'cleanup_frequency' => 'weekly'
                ),
                'requirements_met' => true,
                'reason' => 'Your site has accumulated data that could be cleaned up'
            );
        }

        // WordPress core tweaks
        if (!$current_opts['wordpress-core-tweaks']['enabled']) {
            $recommendations[] = array(
                'id' => 'enable_core_tweaks',
                'title' => 'Enable WordPress Core Optimizations',
                'description' => 'Remove unnecessary WordPress features like emojis and version strings.',
                'priority' => 'low',
                'impact' => 'low',
                'estimated_improvement' => '1-3%',
                'action' => 'enable_module',
                'module' => 'wordpress-core-tweaks',
                'settings' => array(
                    'remove_emojis' => true,
                    'remove_query_strings' => true,
                    'limit_revisions' => true
                ),
                'requirements_met' => true,
                'reason' => 'Safe optimizations that reduce overhead'
            );
        }

        // Sort by priority and impact
        usort($recommendations, function($a, $b) {
            $priority_order = array('high' => 3, 'medium' => 2, 'low' => 1);
            $impact_order = array('high' => 3, 'medium' => 2, 'low' => 1);

            $a_score = $priority_order[$a['priority']] + $impact_order[$a['impact']];
            $b_score = $priority_order[$b['priority']] + $impact_order[$b['impact']];

            return $b_score - $a_score;
        });

        return $recommendations;
    }

    /**
     * Check if there are minification conflicts
     */
    private function has_minification_conflict($conflicts) {
        foreach ($conflicts as $conflict) {
            if (in_array('css-js-minifier', $conflict['conflicts_with'])) {
                return true;
            }
        }
        return false;
    }

    /**
     * Calculate current performance score
     */
    public function calculate_performance_score($analysis) {
        $score = 100;
        $factors = array();

        // Server capabilities (20 points)
        $server_score = 0;
        if ($analysis['server_capabilities']['php_version_adequate']) $server_score += 5;
        if ($analysis['server_capabilities']['memory_adequate']) $server_score += 5;
        if ($analysis['server_capabilities']['opcache_enabled']) $server_score += 5;
        if ($analysis['server_capabilities']['gzip_enabled']) $server_score += 5;
        $factors['server'] = $server_score;

        // Current optimizations (50 points)
        $optimization_score = 0;
        $enabled_count = 0;
        $total_modules = count($analysis['current_optimizations']);

        foreach ($analysis['current_optimizations'] as $module => $config) {
            if ($config['enabled']) {
                $enabled_count++;
                switch ($config['impact']) {
                    case 'high': $optimization_score += 20; break;
                    case 'medium': $optimization_score += 10; break;
                    case 'low': $optimization_score += 5; break;
                }
            }
        }
        $factors['optimizations'] = min(50, $optimization_score);

        // Performance metrics (20 points)
        $performance_score = 20;
        if ($analysis['performance_metrics']['database_performance'] === 'poor') $performance_score -= 7;
        if ($analysis['performance_metrics']['filesystem_performance'] === 'poor') $performance_score -= 7;
        if ($analysis['performance_metrics']['memory_usage_percentage'] > 80) $performance_score -= 6;
        $factors['performance'] = max(0, $performance_score);

        // Plugin conflicts penalty (10 points)
        $conflict_penalty = count($analysis['plugin_conflicts']) * 2;
        $factors['conflicts'] = max(0, 10 - $conflict_penalty);

        $total_score = array_sum($factors);

        return array(
            'total' => $total_score,
            'grade' => $this->get_performance_grade($total_score),
            'factors' => $factors,
            'enabled_modules' => $enabled_count,
            'total_modules' => $total_modules
        );
    }

    /**
     * Get performance grade based on score
     */
    private function get_performance_grade($score) {
        if ($score >= 90) return 'A+';
        if ($score >= 80) return 'A';
        if ($score >= 70) return 'B';
        if ($score >= 60) return 'C';
        if ($score >= 50) return 'D';
        return 'F';
    }

    /**
     * Calculate estimated improvement from recommendations
     */
    public function calculate_estimated_improvement($recommendations) {
        $total_improvement = 0;
        $high_impact_count = 0;

        foreach ($recommendations as $rec) {
            if ($rec['requirements_met']) {
                switch ($rec['impact']) {
                    case 'high':
                        $total_improvement += 30;
                        $high_impact_count++;
                        break;
                    case 'medium':
                        $total_improvement += 15;
                        break;
                    case 'low':
                        $total_improvement += 5;
                        break;
                }
            }
        }

        // Apply diminishing returns for multiple optimizations
        if ($high_impact_count > 1) {
            $total_improvement *= 0.8;
        }

        return array(
            'percentage' => min(80, round($total_improvement)),
            'description' => $this->get_improvement_description($total_improvement),
            'timeframe' => 'immediate'
        );
    }

    /**
     * Get improvement description
     */
    private function get_improvement_description($improvement) {
        if ($improvement >= 50) return 'Significant performance boost expected';
        if ($improvement >= 30) return 'Noticeable performance improvement';
        if ($improvement >= 15) return 'Moderate performance gains';
        return 'Minor performance improvements';
    }

    /**
     * AJAX handler for applying recommendations
     */
    public function ajax_apply_recommendations() {
        // Security check
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce') || !current_user_can('manage_options')) {
            wp_send_json_error('Security check failed');
            return;
        }

        try {
            $selected_recommendations = isset($_POST['recommendations']) ? $_POST['recommendations'] : array();
            $results = array();
            $errors = array();

            foreach ($selected_recommendations as $rec_id) {
                $result = $this->apply_single_recommendation($rec_id, $_POST);
                if ($result['success']) {
                    $results[] = $result;
                } else {
                    $errors[] = $result;
                }
            }

            // Get updated performance score
            $analysis = $this->run_comprehensive_analysis();
            $new_score = $this->calculate_performance_score($analysis);

            wp_send_json_success(array(
                'applied' => $results,
                'errors' => $errors,
                'new_performance_score' => $new_score,
                'message' => sprintf('%d optimizations applied successfully', count($results))
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to apply recommendations: ' . $e->getMessage());
        }
    }

    /**
     * Apply a single recommendation
     */
    private function apply_single_recommendation($rec_id, $post_data) {
        $recommendation_data = isset($post_data['rec_data'][$rec_id]) ? $post_data['rec_data'][$rec_id] : null;

        if (!$recommendation_data) {
            return array('success' => false, 'message' => 'Recommendation data not found');
        }

        switch ($recommendation_data['action']) {
            case 'enable_module':
                return $this->enable_module_with_settings($recommendation_data['module'], $recommendation_data['settings']);
            default:
                return array('success' => false, 'message' => 'Unknown action: ' . $recommendation_data['action']);
        }
    }

    /**
     * Enable a module with specific settings
     */
    private function enable_module_with_settings($module, $settings) {
        try {
            // Get current enabled modules
            $enabled_modules = redco_get_option('modules_enabled', array());

            // Add module if not already enabled
            if (!in_array($module, $enabled_modules)) {
                $enabled_modules[] = $module;
                redco_update_option('modules_enabled', $enabled_modules);
            }

            // Apply module-specific settings
            foreach ($settings as $setting_key => $setting_value) {
                redco_update_module_option($module, $setting_key, $setting_value);
            }

            return array(
                'success' => true,
                'module' => $module,
                'message' => sprintf('Module %s enabled and configured', $module)
            );

        } catch (Exception $e) {
            return array(
                'success' => false,
                'module' => $module,
                'message' => 'Failed to enable module: ' . $e->getMessage()
            );
        }
    }

    /**
     * AJAX handler for getting optimization preview
     */
    public function ajax_get_optimization_preview() {
        // Security check
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce') || !current_user_can('manage_options')) {
            wp_send_json_error('Security check failed');
            return;
        }

        try {
            $selected_recommendations = isset($_POST['recommendations']) ? $_POST['recommendations'] : array();

            // Simulate the changes without actually applying them
            $preview = $this->simulate_optimization_changes($selected_recommendations, $_POST);

            wp_send_json_success($preview);

        } catch (Exception $e) {
            wp_send_json_error('Failed to generate preview: ' . $e->getMessage());
        }
    }

    /**
     * Simulate optimization changes for preview
     */
    private function simulate_optimization_changes($selected_recommendations, $post_data) {
        $current_analysis = $this->run_comprehensive_analysis();
        $current_score = $this->calculate_performance_score($current_analysis);

        // Simulate enabled modules
        $simulated_modules = $current_analysis['current_optimizations'];
        $changes = array();

        foreach ($selected_recommendations as $rec_id) {
            $rec_data = isset($post_data['rec_data'][$rec_id]) ? $post_data['rec_data'][$rec_id] : null;
            if ($rec_data && $rec_data['action'] === 'enable_module') {
                $module = $rec_data['module'];
                $simulated_modules[$module]['enabled'] = true;
                $changes[] = array(
                    'type' => 'module_enabled',
                    'module' => $module,
                    'description' => sprintf('Enable %s module', ucwords(str_replace('-', ' ', $module)))
                );
            }
        }

        // Calculate simulated score
        $simulated_analysis = $current_analysis;
        $simulated_analysis['current_optimizations'] = $simulated_modules;
        $simulated_score = $this->calculate_performance_score($simulated_analysis);

        $improvement = $simulated_score['total'] - $current_score['total'];

        return array(
            'current_score' => $current_score,
            'projected_score' => $simulated_score,
            'improvement' => $improvement,
            'changes' => $changes,
            'summary' => array(
                'modules_to_enable' => count($changes),
                'estimated_improvement' => max(0, $improvement) . ' points',
                'new_grade' => $simulated_score['grade']
            )
        );
    }
}
