<?php
/**
 * Check Database - Direct database inspection of attachment metadata
 */

// Load WordPress
require_once('../../../../../../wp-load.php');

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🗄️ Database Inspection</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .warning { color: orange; } .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; }</style>";

global $wpdb;

// 1. Check the specific problematic attachment
$attachment_id = 15;

echo "<h2>Attachment ID {$attachment_id} Database Analysis</h2>";

// Get the raw metadata from database
$attached_file = $wpdb->get_var($wpdb->prepare("
    SELECT meta_value FROM {$wpdb->postmeta} 
    WHERE post_id = %d AND meta_key = '_wp_attached_file'
", $attachment_id));

echo "<p><strong>_wp_attached_file:</strong> {$attached_file}</p>";

// Check if it's a full path or relative path
$upload_dir = wp_upload_dir();
$is_full_path = (strpos($attached_file, $upload_dir['basedir']) === 0 || 
                strpos($attached_file, 'D:') === 0 || 
                strpos($attached_file, 'C:') === 0);

echo "<p>Is full path: " . ($is_full_path ? "<span class='error'>❌ YES - This is the problem!</span>" : "<span class='success'>✅ NO</span>") . "</p>";

if ($is_full_path) {
    echo "<div class='error'>";
    echo "<h3>🚨 PROBLEM IDENTIFIED</h3>";
    echo "<p>The _wp_attached_file field contains a full file system path instead of a relative path.</p>";
    echo "<p><strong>Current (wrong):</strong> {$attached_file}</p>";
    
    // Calculate what it should be
    $correct_path = str_replace($upload_dir['basedir'] . '/', '', $attached_file);
    $correct_path = str_replace($upload_dir['basedir'] . '\\', '', $correct_path);
    $correct_path = str_replace('\\', '/', $correct_path);
    
    echo "<p><strong>Should be:</strong> {$correct_path}</p>";
    echo "</div>";
    
    // Offer to fix it
    if (isset($_GET['fix']) && $_GET['fix'] === 'yes') {
        $result = update_post_meta($attachment_id, '_wp_attached_file', $correct_path);
        if ($result) {
            echo "<p class='success'>✅ FIXED! Updated _wp_attached_file to relative path.</p>";
            echo "<p><strong>Please refresh the Media Library to see the change.</strong></p>";
        } else {
            echo "<p class='error'>❌ Failed to update metadata.</p>";
        }
    } else {
        echo "<p><a href='?fix=yes' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🔧 Fix This Attachment</a></p>";
    }
}

// 2. Check for other problematic attachments
echo "<h2>Bulk Database Analysis</h2>";

$problematic_count = $wpdb->get_var("
    SELECT COUNT(*) FROM {$wpdb->postmeta} 
    WHERE meta_key = '_wp_attached_file' 
    AND (meta_value LIKE 'D:%' OR meta_value LIKE 'C:%' OR meta_value LIKE '{$upload_dir['basedir']}%')
");

echo "<p><strong>Total problematic attachments:</strong> {$problematic_count}</p>";

if ($problematic_count > 0) {
    $problematic_attachments = $wpdb->get_results("
        SELECT post_id, meta_value FROM {$wpdb->postmeta} 
        WHERE meta_key = '_wp_attached_file' 
        AND (meta_value LIKE 'D:%' OR meta_value LIKE 'C:%' OR meta_value LIKE '{$upload_dir['basedir']}%')
        LIMIT 20
    ");
    
    echo "<h3>Sample Problematic Attachments:</h3>";
    echo "<div class='code'>";
    foreach ($problematic_attachments as $attachment) {
        echo "ID {$attachment->post_id}: {$attachment->meta_value}\n";
    }
    echo "</div>";
    
    if (isset($_GET['fix_all']) && $_GET['fix_all'] === 'yes') {
        echo "<h3>🔧 Bulk Fix Results:</h3>";
        $fixed_count = 0;
        
        foreach ($problematic_attachments as $attachment) {
            $correct_path = str_replace($upload_dir['basedir'] . '/', '', $attachment->meta_value);
            $correct_path = str_replace($upload_dir['basedir'] . '\\', '', $correct_path);
            $correct_path = str_replace('\\', '/', $correct_path);
            
            $result = update_post_meta($attachment->post_id, '_wp_attached_file', $correct_path);
            if ($result) {
                $fixed_count++;
                echo "<p class='success'>✅ Fixed ID {$attachment->post_id}</p>";
            } else {
                echo "<p class='error'>❌ Failed to fix ID {$attachment->post_id}</p>";
            }
        }
        
        echo "<p class='success'><strong>Fixed {$fixed_count} out of " . count($problematic_attachments) . " attachments!</strong></p>";
        echo "<p><strong>Please refresh the Media Library to see the changes.</strong></p>";
        
    } else {
        echo "<p><a href='?fix_all=yes' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🔧 Fix All Problematic Attachments</a></p>";
        echo "<p class='warning'>⚠️ This will update all attachments with full paths to use relative paths.</p>";
    }
}

// 3. Check upload directory configuration
echo "<h2>Upload Directory Configuration</h2>";

echo "<div class='code'>";
foreach ($upload_dir as $key => $value) {
    echo "{$key}: {$value}\n";
}
echo "</div>";

// 4. Test URL generation after potential fix
if (isset($_GET['fix']) || isset($_GET['fix_all'])) {
    echo "<h2>🧪 Test URL Generation After Fix</h2>";
    
    $test_url = wp_get_attachment_url($attachment_id);
    echo "<p><strong>wp_get_attachment_url after fix:</strong> {$test_url}</p>";
    
    $is_proper_url = (strpos($test_url, 'http') === 0);
    echo "<p>Is proper web URL: " . ($is_proper_url ? "<span class='success'>✅ YES</span>" : "<span class='error'>❌ NO</span>") . "</p>";
    
    if ($is_proper_url) {
        echo "<p class='success'>🎉 SUCCESS! The URL is now a proper web URL!</p>";
        echo "<p><strong>Go back to your Media Library and refresh the page to see the fix in action.</strong></p>";
    }
}

// 5. How this problem likely occurred
echo "<h2>📚 How This Problem Occurs</h2>";
echo "<div style='background: #f9f9f9; padding: 15px; border-left: 4px solid #0073aa;'>";
echo "<p><strong>Common causes of full paths in _wp_attached_file:</strong></p>";
echo "<ul>";
echo "<li>Plugin that incorrectly stores full paths during upload</li>";
echo "<li>Manual database import/export that doesn't adjust paths</li>";
echo "<li>WordPress migration that doesn't update attachment metadata</li>";
echo "<li>Custom upload handling code that stores absolute paths</li>";
echo "<li>Server configuration issues during WordPress setup</li>";
echo "</ul>";
echo "<p><strong>The fix:</strong> WordPress expects _wp_attached_file to contain relative paths like <code>2021/06/filename.jpg</code>, not full paths like <code>D:/xampp/htdocs/wordpress/wp-content/uploads/2021/06/filename.jpg</code></p>";
echo "</div>";

echo "<h2>🎯 Summary</h2>";
echo "<p>This tool identifies and fixes the root cause: corrupted attachment metadata that contains full file system paths instead of relative paths.</p>";
echo "<p>After fixing the metadata, WordPress will generate proper web URLs for the Media Library.</p>";
