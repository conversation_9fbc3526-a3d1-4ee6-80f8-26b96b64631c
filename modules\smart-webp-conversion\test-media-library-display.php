<?php
/**
 * Media Library Display Test Suite
 * 
 * This file provides comprehensive testing for the Media Library WebP display functionality
 * to ensure that WebP-converted images display correctly in WordPress admin Media Library.
 * 
 * CRITICAL: Tests the new URL filtering approach that preserves metadata while serving WebP URLs
 * 
 * Usage: Include this file and call the test functions to validate Media Library display.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Media_Library_Display_Test {
    
    private $webp_converter;
    private $test_results = array();
    
    public function __construct() {
        $this->webp_converter = new Redco_Smart_WebP_Conversion();
    }
    
    /**
     * Run comprehensive Media Library display tests
     */
    public function run_all_tests() {
        echo "<h2>🖼️ Media Library WebP Display Test Suite</h2>\n";
        
        $this->test_url_filtering_hooks();
        $this->test_webp_url_generation();
        $this->test_media_library_js_data();
        $this->test_thumbnail_display();
        $this->test_metadata_preservation();
        
        $this->display_results();
    }
    
    /**
     * Test 1: URL Filtering Hooks Registration
     */
    private function test_url_filtering_hooks() {
        echo "<h3>🔗 Test 1: URL Filtering Hooks Registration</h3>\n";
        
        // Check if wp_get_attachment_url hook is registered
        $url_hook = has_filter('wp_get_attachment_url', array($this->webp_converter, 'serve_webp_url_in_media_library'));
        
        if ($url_hook !== false) {
            echo "<p>✅ wp_get_attachment_url hook is properly registered</p>\n";
        } else {
            echo "<p>❌ wp_get_attachment_url hook is NOT registered</p>\n";
        }
        
        // Check if wp_get_attachment_thumb_url hook is registered
        $thumb_hook = has_filter('wp_get_attachment_thumb_url', array($this->webp_converter, 'serve_webp_url_in_media_library'));
        
        if ($thumb_hook !== false) {
            echo "<p>✅ wp_get_attachment_thumb_url hook is properly registered</p>\n";
        } else {
            echo "<p>❌ wp_get_attachment_thumb_url hook is NOT registered</p>\n";
        }
        
        // Check if wp_prepare_attachment_for_js hook is registered
        $js_hook = has_filter('wp_prepare_attachment_for_js', array($this->webp_converter, 'fix_media_library_webp_urls'));
        
        if ($js_hook !== false) {
            echo "<p>✅ wp_prepare_attachment_for_js hook is properly registered</p>\n";
        } else {
            echo "<p>❌ wp_prepare_attachment_for_js hook is NOT registered</p>\n";
        }
        
        $this->test_results['url_filtering_hooks'] = array(
            'wp_get_attachment_url' => $url_hook !== false,
            'wp_get_attachment_thumb_url' => $thumb_hook !== false,
            'wp_prepare_attachment_for_js' => $js_hook !== false
        );
    }
    
    /**
     * Test 2: WebP URL Generation
     */
    private function test_webp_url_generation() {
        echo "<h3>🔗 Test 2: WebP URL Generation</h3>\n";
        
        global $wpdb;
        
        // Get a converted attachment for testing
        $converted_attachment = $wpdb->get_row("
            SELECT p.ID 
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
            AND pm.meta_value LIKE '%\"converted\":true%'
            LIMIT 1
        ");
        
        if (!$converted_attachment) {
            echo "<p>⚠️ No converted attachments found for testing</p>\n";
            $this->test_results['webp_url_generation'] = array('tested' => false);
            return;
        }
        
        $attachment_id = $converted_attachment->ID;
        echo "<p>Testing with attachment ID: {$attachment_id}</p>\n";
        
        // Test original URL vs WebP URL
        $original_url = wp_get_attachment_url($attachment_id);
        echo "<p>Original URL: " . basename($original_url) . "</p>\n";
        
        // Check if URL filtering is working
        if (strpos($original_url, '.webp') !== false) {
            echo "<p>✅ URL filtering is working - serving WebP URL</p>\n";
            $url_filtering_works = true;
        } else {
            echo "<p>❌ URL filtering is NOT working - still serving original URL</p>\n";
            $url_filtering_works = false;
        }
        
        // Test thumbnail URL
        $thumb_url = wp_get_attachment_thumb_url($attachment_id);
        if ($thumb_url) {
            echo "<p>Thumbnail URL: " . basename($thumb_url) . "</p>\n";
            
            if (strpos($thumb_url, '.webp') !== false) {
                echo "<p>✅ Thumbnail URL filtering is working - serving WebP thumbnail</p>\n";
                $thumb_filtering_works = true;
            } else {
                echo "<p>❌ Thumbnail URL filtering is NOT working - still serving original thumbnail</p>\n";
                $thumb_filtering_works = false;
            }
        } else {
            echo "<p>⚠️ No thumbnail URL available for testing</p>\n";
            $thumb_filtering_works = false;
        }
        
        $this->test_results['webp_url_generation'] = array(
            'tested' => true,
            'attachment_id' => $attachment_id,
            'url_filtering_works' => $url_filtering_works,
            'thumb_filtering_works' => $thumb_filtering_works
        );
    }
    
    /**
     * Test 3: Media Library JavaScript Data
     */
    private function test_media_library_js_data() {
        echo "<h3>📱 Test 3: Media Library JavaScript Data</h3>\n";
        
        global $wpdb;
        
        // Get a converted attachment for testing
        $converted_attachment = $wpdb->get_row("
            SELECT p.ID 
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
            AND pm.meta_value LIKE '%\"converted\":true%'
            LIMIT 1
        ");
        
        if (!$converted_attachment) {
            echo "<p>⚠️ No converted attachments found for testing</p>\n";
            $this->test_results['media_library_js_data'] = array('tested' => false);
            return;
        }
        
        $attachment_id = $converted_attachment->ID;
        $attachment = get_post($attachment_id);
        
        // Test wp_prepare_attachment_for_js filter
        $js_data = wp_prepare_attachment_for_js($attachment);
        
        if ($js_data && isset($js_data['url'])) {
            echo "<p>JavaScript data URL: " . basename($js_data['url']) . "</p>\n";
            
            if (strpos($js_data['url'], '.webp') !== false) {
                echo "<p>✅ JavaScript data filtering is working - serving WebP URL</p>\n";
                $js_filtering_works = true;
            } else {
                echo "<p>❌ JavaScript data filtering is NOT working - still serving original URL</p>\n";
                $js_filtering_works = false;
            }
            
            // Test size URLs
            $webp_sizes_count = 0;
            if (isset($js_data['sizes']) && is_array($js_data['sizes'])) {
                foreach ($js_data['sizes'] as $size_name => $size_data) {
                    if (isset($size_data['url']) && strpos($size_data['url'], '.webp') !== false) {
                        $webp_sizes_count++;
                    }
                }
                
                echo "<p>WebP size URLs found: {$webp_sizes_count} out of " . count($js_data['sizes']) . " sizes</p>\n";
            }
        } else {
            echo "<p>❌ No JavaScript data available for testing</p>\n";
            $js_filtering_works = false;
            $webp_sizes_count = 0;
        }
        
        $this->test_results['media_library_js_data'] = array(
            'tested' => true,
            'attachment_id' => $attachment_id,
            'js_filtering_works' => $js_filtering_works,
            'webp_sizes_count' => $webp_sizes_count
        );
    }
    
    /**
     * Test 4: Thumbnail Display Verification
     */
    private function test_thumbnail_display() {
        echo "<h3>🖼️ Test 4: Thumbnail Display Verification</h3>\n";
        
        global $wpdb;
        
        // Get converted attachments
        $converted_attachments = $wpdb->get_results("
            SELECT p.ID 
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
            AND pm.meta_value LIKE '%\"converted\":true%'
            LIMIT 3
        ");
        
        if (empty($converted_attachments)) {
            echo "<p>⚠️ No converted attachments found for testing</p>\n";
            $this->test_results['thumbnail_display'] = array('tested' => false);
            return;
        }
        
        $working_thumbnails = 0;
        $total_thumbnails = count($converted_attachments);
        
        foreach ($converted_attachments as $attachment) {
            $attachment_id = $attachment->ID;
            
            // Test wp_get_attachment_image_src
            $image_src = wp_get_attachment_image_src($attachment_id, 'thumbnail');
            
            if ($image_src && isset($image_src[0])) {
                $thumbnail_url = $image_src[0];
                
                if (strpos($thumbnail_url, '.webp') !== false) {
                    $working_thumbnails++;
                    echo "<p>✅ Attachment {$attachment_id}: WebP thumbnail URL working</p>\n";
                } else {
                    echo "<p>❌ Attachment {$attachment_id}: Still serving original thumbnail</p>\n";
                }
            } else {
                echo "<p>⚠️ Attachment {$attachment_id}: No thumbnail data available</p>\n";
            }
        }
        
        echo "<p>📊 Summary: {$working_thumbnails} out of {$total_thumbnails} thumbnails serving WebP</p>\n";
        
        $this->test_results['thumbnail_display'] = array(
            'tested' => true,
            'working_thumbnails' => $working_thumbnails,
            'total_thumbnails' => $total_thumbnails,
            'success_ratio' => $total_thumbnails > 0 ? $working_thumbnails / $total_thumbnails : 0
        );
    }
    
    /**
     * Test 5: Metadata Preservation Verification
     */
    private function test_metadata_preservation() {
        echo "<h3>🔒 Test 5: Metadata Preservation Verification</h3>\n";
        
        global $wpdb;
        
        // Get converted attachments
        $converted_attachments = $wpdb->get_results("
            SELECT p.ID 
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = '_webp_conversion_data'
            AND pm.meta_value LIKE '%\"converted\":true%'
            LIMIT 3
        ");
        
        if (empty($converted_attachments)) {
            echo "<p>⚠️ No converted attachments found for testing</p>\n";
            $this->test_results['metadata_preservation'] = array('tested' => false);
            return;
        }
        
        $preserved_metadata_count = 0;
        $total_attachments = count($converted_attachments);
        
        foreach ($converted_attachments as $attachment) {
            $attachment_id = $attachment->ID;
            
            // Check WordPress core metadata
            $wp_attached_file = get_post_meta($attachment_id, '_wp_attached_file', true);
            
            if ($wp_attached_file && !strpos($wp_attached_file, '.webp')) {
                $preserved_metadata_count++;
                echo "<p>✅ Attachment {$attachment_id}: WordPress metadata preserved (points to original)</p>\n";
            } else {
                echo "<p>❌ Attachment {$attachment_id}: WordPress metadata corrupted (points to WebP)</p>\n";
            }
        }
        
        echo "<p>📊 Summary: {$preserved_metadata_count} out of {$total_attachments} have preserved metadata</p>\n";
        
        $this->test_results['metadata_preservation'] = array(
            'tested' => true,
            'preserved_metadata_count' => $preserved_metadata_count,
            'total_attachments' => $total_attachments,
            'preservation_ratio' => $total_attachments > 0 ? $preserved_metadata_count / $total_attachments : 0
        );
    }
    
    /**
     * Display comprehensive test results
     */
    private function display_results() {
        echo "<h3>📊 Test Results Summary</h3>\n";
        
        foreach ($this->test_results as $test_name => $results) {
            echo "<h4>" . ucwords(str_replace('_', ' ', $test_name)) . "</h4>\n";
            echo "<pre>" . print_r($results, true) . "</pre>\n";
        }
        
        // Overall assessment
        $critical_issues = 0;
        
        if (isset($this->test_results['url_filtering_hooks'])) {
            $hooks = $this->test_results['url_filtering_hooks'];
            if (!$hooks['wp_get_attachment_url'] || !$hooks['wp_prepare_attachment_for_js']) {
                $critical_issues++;
            }
        }
        
        if (isset($this->test_results['thumbnail_display']['success_ratio'])) {
            if ($this->test_results['thumbnail_display']['success_ratio'] < 0.8) {
                $critical_issues++;
            }
        }
        
        if (isset($this->test_results['metadata_preservation']['preservation_ratio'])) {
            if ($this->test_results['metadata_preservation']['preservation_ratio'] < 1.0) {
                $critical_issues++;
            }
        }
        
        echo "<h3>🎯 Overall Assessment</h3>\n";
        if ($critical_issues === 0) {
            echo "<p style='color: green; font-weight: bold;'>✅ ALL TESTS PASSED - Media Library WebP display is working correctly!</p>\n";
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ {$critical_issues} CRITICAL ISSUES FOUND - Media Library display needs attention!</p>\n";
        }
    }
}

// Usage example (uncomment to run tests)
// if (is_admin() && current_user_can('manage_options')) {
//     $test_suite = new Redco_Media_Library_Display_Test();
//     $test_suite->run_all_tests();
// }
